import axios from 'axios';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

const API_URL = __DEV__ 
  ? 'http://192.168.184.252:8000'  // Your local development server
  : 'https://your-production-server.com';  // Replace with your production URL

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
    'X-Device-Type': Platform.OS,
    'X-App-Version': '1.0.0',
  },
});

export const ragService = {
  async query(question) {
    try {
      const deviceInfo = {
        platform: Platform.OS,
        version: Platform.Version,
        model: Platform.select({
          ios: 'iOS Device',
          android: 'Android Device',
        }),
      };

      const response = await api.post('/mobile/query', {
        query: question,
        device_info: deviceInfo,
      });

      return response.data;
    } catch (error) {
      console.error('Query error:', error);
      throw error;
    }
  },

  async getConfig() {
    try {
      const response = await api.get('/mobile/config');
      return response.data;
    } catch (error) {
      console.error('Config fetch error:', error);
      throw error;
    }
  },

  async getCachedResponse(question) {
    try {
      const cached = await AsyncStorage.getItem(`query_${question}`);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      console.error('Cache read error:', error);
      return null;
    }
  },

  async cacheResponse(question, response) {
    try {
      await AsyncStorage.setItem(
        `query_${question}`,
        JSON.stringify(response)
      );
    } catch (error) {
      console.error('Cache write error:', error);
    }
  },
}; 