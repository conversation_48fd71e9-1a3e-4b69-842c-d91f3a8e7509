/* Custom styles for Virat Gyankosh */

/* CSS Variables for Theme Support */
:root {
    /* Light Theme Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --border-color: #e2e8f0;
    --border-light: rgba(0, 0, 0, 0.1);
    --shadow-light: rgba(0, 0, 0, 0.1);
    --shadow-medium: rgba(0, 0, 0, 0.15);
    --accent-primary: #3b82f6;
    --accent-secondary: #10b981;
    --accent-danger: #ef4444;
    --accent-warning: #f59e0b;

    /* Chat Specific Colors */
    --chat-bg: rgba(255, 255, 255, 0.95);
    --chat-sidebar-bg: #f8fafc;
    --chat-header-bg: rgba(255, 255, 255, 0.9);
    --message-user-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --message-ai-bg: #f1f5f9;
    --input-bg: #ffffff;
    --input-border: #e2e8f0;
    --input-focus: #3b82f6;
}

/* Dark Theme Colors */
[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --border-color: #334155;
    --border-light: rgba(255, 255, 255, 0.1);
    --shadow-light: rgba(0, 0, 0, 0.3);
    --shadow-medium: rgba(0, 0, 0, 0.4);
    --accent-primary: #60a5fa;
    --accent-secondary: #34d399;
    --accent-danger: #f87171;
    --accent-warning: #fbbf24;

    /* Chat Specific Colors */
    --chat-bg: rgba(15, 23, 42, 0.95);
    --chat-sidebar-bg: #1e293b;
    --chat-header-bg: rgba(30, 41, 59, 0.9);
    --message-user-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --message-ai-bg: #334155;
    --input-bg: #334155;
    --input-border: #475569;
    --input-focus: #60a5fa;
}

/* Theme Transition */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Chat message styles */
.message {
    padding: 1rem;
    margin: 0.5rem 0;
    border-radius: 0.5rem;
    max-width: 80%;
}

.user-message {
    background-color: #4F46E5;
    color: white;
    margin-left: auto;
}

.assistant-message {
    background-color: #F3F4F6;
    color: #1F2937;
}

.message-metadata {
    font-size: 0.75rem;
    color: #6B7280;
    margin-top: 0.25rem;
}

/* Source citation styles */
.source-citation {
    font-size: 0.875rem;
    color: #6B7280;
    border-top: 1px solid #E5E7EB;
    margin-top: 0.5rem;
    padding-top: 0.5rem;
}

/* Loading indicator */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.loading-dots {
    display: flex;
    gap: 0.5rem;
}

.loading-dots span {
    width: 0.5rem;
    height: 0.5rem;
    background-color: #4F46E5;
    border-radius: 50%;
    animation: bounce 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes bounce {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

/* Error message styles */
.error-message {
    background-color: #FEE2E2;
    color: #991B1B;
    padding: 1rem;
    border-radius: 0.5rem;
    margin: 0.5rem 0;
    border: 1px solid #F87171;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .message {
        max-width: 90%;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.message {
    animation: fadeIn 0.3s ease-out;
}

/* Focus styles */
.focus-ring {
    outline: none;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

input:focus {
    outline: none;
    border-color: #4F46E5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* Educational RAG AI App - Futuristic Landing Page Styles */

/* CSS Custom Properties */
:root {
    /* Color Palette */
    --primary-blue: #00d4ff;
    --primary-purple: #8b5cf6;
    --secondary-navy: #0a0a23;
    --secondary-deep-purple: #6c63ff;
    --accent-cyan: #06ffa5;
    --success-green: #10b981;
    --warning-orange: #f59e0b;
    --error-red: #ef4444;
    
    /* Gradients */
    --hero-gradient: linear-gradient(135deg, #0a0a23 0%, #6c63ff 100%);
    --button-gradient: linear-gradient(135deg, #00d4ff 0%, #8b5cf6 100%);
    --accent-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #10b981 0%, #06ffa5 100%);
    
    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-heading: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    
    /* Spacing */
    --section-padding: 120px 0;
    --container-padding: 0 24px;
    --card-padding: 32px;
    
    /* Glass Effects */
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.18);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    --glass-blur: 15px;
    
    /* Animation Timings */
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Breakpoints */
    --mobile: 768px;
    --tablet: 1024px;
    --desktop: 1200px;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
    background: var(--hero-gradient);
    min-height: 100vh;
}

/* Typography System */
.hero-title {
    font-size: clamp(2rem, 5vw, 3rem);
    font-weight: 700;
    line-height: 1.2;
    color: white;
    margin-bottom: 24px;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: clamp(1rem, 2.5vw, 1.125rem);
    font-weight: 400;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 40px;
    max-width: 600px;
    line-height: 1.6;
}

.section-title {
    font-size: clamp(1.75rem, 4vw, 2rem);
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 16px;
    text-align: center;
}

.section-title.dark {
    color: #1e293b;
}

.section-title.light {
    color: white;
}

.section-subtitle {
    font-size: 1.125rem;
    color: #64748b;
    text-align: center;
    margin-bottom: 64px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.section-subtitle.dark {
    color: #475569;
}

.section-subtitle.light {
    color: rgba(255, 255, 255, 0.8);
}

.card-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 16px;
}

.card-description {
    font-size: 1rem;
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 24px;
}

/* Layout Components */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--container-padding);
}

.section-header {
    text-align: center;
    margin-bottom: 80px;
}

/* Glass Card Component */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(var(--glass-blur));
    -webkit-backdrop-filter: blur(var(--glass-blur));
    border-radius: 20px;
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    padding: var(--card-padding);
    transition: all var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.glass-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
}

.glass-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px 0 rgba(31, 38, 135, 0.4);
    background: rgba(255, 255, 255, 0.3);
}

/* Button System */
.btn-primary, .btn-secondary, .btn-accent, .btn-success {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    padding: 16px 32px;
    border-radius: 12px;
    border: none;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-medium);
    position: relative;
    overflow: hidden;
    min-height: 56px;
    font-family: var(--font-primary);
}

.btn-primary {
    background: var(--button-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 212, 255, 0.4);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.6);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.btn-accent {
    background: var(--accent-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: var(--success-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    padding: var(--section-padding);
    background: var(--hero-gradient);
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle at 30% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(6, 255, 165, 0.05) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
    z-index: 0;
}

.hero-section .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-content {
    max-width: 600px;
}

.hero-buttons {
    display: flex;
    gap: 24px;
    flex-wrap: wrap;
}

.hero-visual {
    position: relative;
    height: 500px;
}

.floating-card {
    position: absolute;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    border: 1px solid var(--glass-border);
    padding: 20px;
    box-shadow: var(--glass-shadow);
    display: flex;
    align-items: center;
    gap: 16px;
    min-width: 200px;
    animation: floating 6s ease-in-out infinite;
}

.floating-card.card-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-card.card-2 {
    top: 60%;
    right: 20%;
    animation-delay: 2s;
}

.floating-card.card-3 {
    top: 40%;
    left: 60%;
    animation-delay: 4s;
}

.floating-card .card-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: var(--button-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.floating-card h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: white;
    margin: 0;
}

.floating-card p {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

/* Benefits Section */
.benefits-section {
    padding: var(--section-padding);
    background: white;
    position: relative;
}

.benefits-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.02) 0%, rgba(139, 92, 246, 0.02) 100%);
    z-index: 0;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    position: relative;
    z-index: 1;
}

.benefit-card {
    text-align: center;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.card-icon-large {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    background: var(--button-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    position: relative;
    overflow: hidden;
}

.card-icon-large::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 2s infinite;
}

.gradient-icon {
    font-size: 32px;
    color: white;
    z-index: 1;
    position: relative;
}

/* Chat Preview Component */
.chat-preview {
    margin-top: 24px;
    text-align: left;
}

.chat-bubble {
    padding: 12px 16px;
    border-radius: 18px;
    margin-bottom: 8px;
    max-width: 80%;
    font-size: 0.875rem;
    line-height: 1.4;
}

.chat-bubble.user {
    background: var(--button-gradient);
    color: white;
    margin-left: auto;
    border-bottom-right-radius: 4px;
}

.chat-bubble.ai {
    background: #f1f5f9;
    color: #334155;
    border-bottom-left-radius: 4px;
}

/* Notes Preview Component */
.notes-preview {
    margin-top: 24px;
    text-align: left;
}

.note-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    font-size: 0.875rem;
    color: #64748b;
}

.note-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--button-gradient);
    flex-shrink: 0;
}

/* Thinking Map Component */
.thinking-map {
    position: relative;
    height: 120px;
    margin-top: 24px;
}

.thinking-node {
    position: absolute;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 0.75rem;
    font-weight: 500;
    color: #475569;
    white-space: nowrap;
}

.thinking-node.center {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--button-gradient);
    color: white;
    font-weight: 600;
}

.thinking-node.node-1 {
    top: 10%;
    left: 20%;
}

.thinking-node.node-2 {
    top: 10%;
    right: 20%;
}

.thinking-node.node-3 {
    bottom: 10%;
    left: 50%;
    transform: translateX(-50%);
}

.connection {
    position: absolute;
    background: var(--button-gradient);
    height: 2px;
    border-radius: 1px;
}

.connection.con-1 {
    top: 30%;
    left: 35%;
    width: 30%;
    transform: rotate(-15deg);
}

.connection.con-2 {
    top: 30%;
    right: 35%;
    width: 30%;
    transform: rotate(15deg);
}

.connection.con-3 {
    bottom: 30%;
    left: 50%;
    width: 30%;
    transform: translateX(-50%) rotate(90deg);
}

/* Parent Benefits Section */
.parent-benefits-section {
    padding: var(--section-padding);
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
}

.parent-benefits-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

.parent-content {
    text-align: left;
}

.parent-content .section-title,
.parent-content .section-subtitle {
    text-align: left;
    margin-bottom: 32px;
}

.parent-features {
    display: grid;
    gap: 32px;
}

.feature-item {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.feature-icon {
    width: 56px;
    height: 56px;
    border-radius: 14px;
    background: var(--button-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.feature-content h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 8px;
}

.feature-content p {
    font-size: 1rem;
    color: #64748b;
    line-height: 1.6;
}

/* Dashboard Component */
.dashboard-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.dashboard-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 24px;
    text-align: center;
}

.dashboard-stats {
    display: grid;
    gap: 24px;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 0.875rem;
    color: #64748b;
    margin-bottom: 8px;
}

.stat-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.stat-fill {
    height: 100%;
    background: var(--button-gradient);
    border-radius: 4px;
    transition: width 1s ease;
}

.stat-trend {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    font-size: 0.75rem;
    color: var(--success-green);
}

/* Technical Features Section */
.tech-features-section {
    padding: var(--section-padding);
    background: #1e293b;
    position: relative;
    overflow: hidden;
}

.tech-features-section::before {
    content: '';
    position: absolute;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle at 70% 30%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 20% 70%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
    animation: float 25s ease-in-out infinite reverse;
    z-index: 0;
}

.tech-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 32px;
    position: relative;
    z-index: 1;
}

.tech-card {
    text-align: center;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-medium);
}

.tech-card:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-8px) scale(1.02);
}

.tech-icon {
    width: 64px;
    height: 64px;
    border-radius: 16px;
    background: var(--button-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 24px;
    color: white;
}

.tech-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: white;
    margin-bottom: 16px;
}

.tech-stat {
    font-size: 2.5rem;
    font-weight: 700;
    background: var(--button-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 8px;
}

.tech-card p {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 16px;
}

.tech-detail {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.6);
}

.tech-models {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin: 16px 0;
}

.model-badge {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.performance-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
    margin: 16px 0;
}

.performance-fill {
    width: 95%;
    height: 100%;
    background: var(--button-gradient);
    animation: loadBar 2s ease-out;
}

.coverage-card {
    grid-column: span 2;
}

.coverage-stats {
    display: grid;
    gap: 16px;
    margin-top: 20px;
}

.coverage-item {
    display: grid;
    grid-template-columns: 1fr 3fr auto;
    gap: 12px;
    align-items: center;
}

.coverage-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.875rem;
}

.coverage-bar {
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
}

.coverage-fill {
    height: 100%;
    background: var(--button-gradient);
    border-radius: 4px;
    transition: width 1.5s ease;
}

.coverage-percent {
    color: white;
    font-size: 0.875rem;
    font-weight: 600;
}

/* CTA Section */
.cta-section {
    padding: var(--section-padding);
    background: var(--hero-gradient);
    text-align: center;
}

.cta-title {
    font-size: clamp(1.75rem, 4vw, 2.5rem);
    font-weight: 700;
    color: white;
    margin-bottom: 16px;
}

.cta-subtitle {
    font-size: 1.125rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 48px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
    justify-content: center;
}

.cta-btn {
    min-width: 200px;
}

/* Chat Demo Section */
.chat-demo-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.chat-demo-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.chat-demo-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
    align-items: start;
    position: relative;
    z-index: 1;
}

.chat-demo-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.chat-demo-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.chat-demo-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    color: #1e293b;
}

.chat-demo-title i {
    color: var(--primary-blue);
    font-size: 1.2rem;
}

.chat-demo-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.875rem;
    color: #10b981;
}

.status-dot {
    width: 8px;
    height: 8px;
    background: #10b981;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.chat-demo-messages {
    padding: 24px;
    max-height: 300px;
    overflow-y: auto;
}

.demo-message {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
    align-items: flex-start;
}

.demo-message.user-message {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.ai-message .message-avatar {
    background: var(--button-gradient);
    color: white;
}

.user-message .message-avatar {
    background: #e2e8f0;
    color: #64748b;
}

.message-content {
    background: #f8fafc;
    padding: 12px 16px;
    border-radius: 16px;
    max-width: 80%;
    font-size: 0.875rem;
    line-height: 1.5;
}

.user-message .message-content {
    background: var(--button-gradient);
    color: white;
    border-bottom-right-radius: 4px;
}

.ai-message .message-content {
    border-bottom-left-radius: 4px;
}

.message-sources {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.message-sources small {
    color: #64748b;
    display: flex;
    align-items: center;
    gap: 4px;
}

.chat-demo-input {
    padding: 24px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    text-align: center;
}

.chat-demo-btn {
    width: 100%;
    margin-bottom: 12px;
}

.chat-demo-note {
    font-size: 0.875rem;
    color: #64748b;
    margin: 0;
}

.chat-features {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.feature-highlight {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    padding: 24px;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.feature-highlight .feature-icon {
    width: 60px;
    height: 60px;
    background: var(--button-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    font-size: 1.5rem;
    color: white;
}

.feature-highlight h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 8px;
}

.feature-highlight p {
    font-size: 0.875rem;
    color: #64748b;
    margin: 0;
}

/* Floating Chat Button */
.floating-chat-btn {
    position: fixed;
    bottom: 24px;
    right: 24px;
    z-index: 999;
    cursor: pointer;
    transition: all var(--transition-medium);
}

.chat-btn-content {
    background: var(--button-gradient);
    color: white;
    padding: 16px 20px;
    border-radius: 50px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4);
    transition: all var(--transition-medium);
}

.floating-chat-btn:hover .chat-btn-content {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.6);
}

.chat-btn-text {
    font-weight: 600;
    font-size: 0.875rem;
}

.chat-btn-notification {
    position: absolute;
    bottom: 100%;
    right: 0;
    background: #1e293b;
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 0.75rem;
    white-space: nowrap;
    margin-bottom: 8px;
    opacity: 0;
    transform: translateY(10px);
    transition: all var(--transition-medium);
    pointer-events: none;
}

.chat-btn-notification::after {
    content: '';
    position: absolute;
    top: 100%;
    right: 16px;
    border: 4px solid transparent;
    border-top-color: #1e293b;
}

.floating-chat-btn:hover .chat-btn-notification {
    opacity: 1;
    transform: translateY(0);
}

/* Modern Chat Interface */
.chat-interface {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.chat-interface.active {
    opacity: 1;
    visibility: visible;
}

.modern-chat-container {
    width: 95%;
    max-width: 1200px;
    height: 85vh;
    background: var(--chat-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-light);
    border-radius: 20px;
    display: flex;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
    box-shadow: 0 25px 50px var(--shadow-medium);
}

.chat-interface.active .modern-chat-container {
    transform: scale(1);
}

/* Chat Sidebar */
.chat-sidebar {
    width: 300px;
    background: var(--chat-sidebar-bg);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;
}

.chat-sidebar.collapsed {
    transform: translateX(-100%);
    width: 0;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-header h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.new-chat-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: var(--accent-primary);
    color: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.new-chat-btn:hover {
    transform: scale(1.05);
    background: var(--accent-primary);
    filter: brightness(1.1);
}

.chat-search {
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
    position: relative;
}

.search-input {
    width: 100%;
    padding: 10px 16px 10px 40px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.875rem;
}

.search-input:focus {
    outline: none;
    border-color: var(--accent-primary);
}

.search-icon {
    position: absolute;
    left: 32px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    font-size: 0.875rem;
}

.chat-history-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
}

.chat-history-item {
    padding: 12px 16px;
    margin-bottom: 4px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-left: 3px solid transparent;
}

.chat-history-item:hover {
    background: var(--bg-tertiary);
}

.chat-history-item.active {
    background: var(--bg-tertiary);
    border-left-color: var(--accent-primary);
}

.history-item-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 4px;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.history-item-preview {
    font-size: 0.75rem;
    color: var(--text-muted);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.history-item-time {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-top: 4px;
}

.sidebar-footer {
    padding: 16px 20px;
    border-top: 1px solid var(--border-color);
}

.clear-history-btn {
    width: 100%;
    padding: 10px 16px;
    border: 1px solid var(--accent-danger);
    background: transparent;
    color: var(--accent-danger);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.clear-history-btn:hover {
    background: var(--accent-danger);
    color: white;
}

/* Main Chat Area */
.chat-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--bg-primary);
}

/* Enhanced Chat Header */
.chat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    background: var(--chat-header-bg);
    border-bottom: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.sidebar-toggle-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.sidebar-toggle-btn:hover {
    background: var(--accent-primary);
    color: white;
}

.chat-title h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 4px 0;
}

.chat-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-dot.online {
    background: var(--accent-secondary);
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.control-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.control-btn:hover {
    background: var(--accent-primary);
    color: white;
    transform: scale(1.05);
}

.theme-toggle {
    position: relative;
}

.theme-toggle.dark-mode i::before {
    content: "\f185"; /* sun icon */
}

.close-btn:hover {
    background: var(--accent-danger) !important;
}

/* Messages Area */
.chat-messages {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
    background: var(--bg-secondary);
    display: flex;
    flex-direction: column;
    gap: 16px;
}

/* Modern Message Styles */
.message {
    display: flex;
    gap: 12px;
    max-width: 80%;
    animation: messageSlideIn 0.3s ease;
}

.message.user-message {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.message.assistant-message {
    align-self: flex-start;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
    box-shadow: 0 2px 8px var(--shadow-light);
}

.user-message .message-avatar {
    background: var(--message-user-bg);
    color: white;
}

.assistant-message .message-avatar {
    background: var(--accent-primary);
    color: white;
}

.message-content {
    background: var(--message-ai-bg);
    padding: 16px 20px;
    border-radius: 18px;
    font-size: 0.95rem;
    line-height: 1.5;
    color: var(--text-primary);
    box-shadow: 0 2px 8px var(--shadow-light);
    position: relative;
}

.user-message .message-content {
    background: var(--message-user-bg);
    color: white;
    border-bottom-right-radius: 6px;
}

.assistant-message .message-content {
    border-bottom-left-radius: 6px;
}

.message-metadata {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.message-actions {
    display: flex;
    gap: 4px;
    margin-top: 8px;
}

.message-action-btn {
    width: 24px;
    height: 24px;
    border: none;
    background: transparent;
    color: var(--text-muted);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.75rem;
}

.message-action-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Enhanced Input Area */
.chat-input-area {
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color);
    padding: 16px 24px;
}

.file-preview {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 12px 16px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.file-preview-content {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.file-info i {
    color: var(--accent-primary);
    font-size: 1.2rem;
}

.file-name {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.file-size {
    color: var(--text-muted);
    font-size: 0.75rem;
}

.remove-file-btn {
    width: 24px;
    height: 24px;
    border: none;
    background: var(--accent-danger);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.remove-file-btn:hover {
    transform: scale(1.1);
}

/* Voice Recording */
.voice-recording {
    background: var(--accent-danger);
    color: white;
    border-radius: 12px;
    padding: 12px 16px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    animation: pulse 1s infinite;
}

.recording-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.recording-dot {
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
    animation: pulse 1s infinite;
}

.recording-timer {
    font-weight: 600;
    font-family: monospace;
}

/* Input Form */
.chat-input-form {
    margin-bottom: 12px;
}

.input-container {
    display: flex;
    align-items: center;
    gap: 8px;
    background: var(--input-bg);
    border: 2px solid var(--input-border);
    border-radius: 16px;
    padding: 8px 12px;
    transition: border-color 0.2s ease;
}

.input-container:focus-within {
    border-color: var(--input-focus);
}

.input-action-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.input-action-btn:hover {
    background: var(--bg-tertiary);
    color: var(--accent-primary);
}

.voice-btn.recording {
    background: var(--accent-danger);
    color: white;
    animation: pulse 1s infinite;
}

.chat-input {
    flex: 1;
    border: none;
    background: transparent;
    color: var(--text-primary);
    font-size: 1rem;
    font-family: var(--font-primary);
    padding: 8px 12px;
    outline: none;
}

.chat-input::placeholder {
    color: var(--text-muted);
}

.send-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--accent-primary);
    color: white;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.send-btn:hover {
    background: var(--accent-primary);
    filter: brightness(1.1);
    transform: scale(1.05);
}

.send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* Quick Actions */
.quick-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.quick-action-btn {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 8px 12px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.quick-action-btn:hover {
    background: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
}

.quick-action-btn i {
    font-size: 0.75rem;
}

/* Emoji Picker */
.emoji-picker {
    position: absolute;
    bottom: 80px;
    right: 24px;
    width: 300px;
    height: 350px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    box-shadow: 0 10px 30px var(--shadow-medium);
    z-index: 1001;
    overflow: hidden;
}

.emoji-categories {
    display: flex;
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
}

.emoji-category {
    flex: 1;
    padding: 12px;
    border: none;
    background: transparent;
    font-size: 1.2rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.emoji-category:hover,
.emoji-category.active {
    background: var(--accent-primary);
}

.emoji-grid {
    padding: 16px;
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 8px;
    height: calc(100% - 60px);
    overflow-y: auto;
}

.emoji-item {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    font-size: 1.2rem;
    cursor: pointer;
    border-radius: 6px;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.emoji-item:hover {
    background: var(--bg-tertiary);
}

/* Animations */
@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.05);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideInFromRight {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes slideInFromLeft {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* Loading Animation */
.loading-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--accent-primary);
    display: inline-block;
    animation: loadingDots 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) {
    animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes loadingDots {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.fade-in {
    animation: fadeIn 0.3s ease;
}

.slide-in-right {
    animation: slideInFromRight 0.3s ease;
}

.slide-in-left {
    animation: slideInFromLeft 0.3s ease;
}

/* Responsive Design for Modern Chat */
@media (max-width: 768px) {
    .modern-chat-container {
        width: 100%;
        height: 100vh;
        border-radius: 0;
        max-width: none;
    }

    .chat-sidebar {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        z-index: 10;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .chat-sidebar.open {
        transform: translateX(0);
    }

    .chat-main {
        width: 100%;
    }

    .header-controls {
        gap: 4px;
    }

    .control-btn {
        width: 32px;
        height: 32px;
    }

    .chat-messages {
        padding: 16px;
    }

    .chat-input-area {
        padding: 12px 16px;
    }

    .quick-actions {
        display: none;
    }

    .emoji-picker {
        width: 280px;
        right: 16px;
    }

    .message {
        max-width: 90%;
    }

    .input-container {
        padding: 6px 10px;
    }

    .input-action-btn {
        width: 32px;
        height: 32px;
    }

    .send-btn {
        width: 36px;
        height: 36px;
    }
}

@media (max-width: 480px) {
    .chat-sidebar {
        width: 280px;
    }

    .chat-input-area {
        padding: 8px 12px;
    }

    .emoji-picker {
        width: 260px;
        height: 300px;
    }

    .quick-action-btn {
        font-size: 0.75rem;
        padding: 6px 10px;
    }
}

/* Animations */
@keyframes floating {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes float {
    0%, 100% { transform: translateX(0px) rotate(0deg); }
    33% { transform: translateX(30px) rotate(1deg); }
    66% { transform: translateX(-20px) rotate(-1deg); }
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

@keyframes loadBar {
    0% { width: 0%; }
    100% { width: 95%; }
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.fade-in {
    animation: fadeInUp 0.6s ease forwards;
}

.pulse {
    animation: pulse 2s infinite;
}

/* Responsive Design */
@media (max-width: 1024px) {
    :root {
        --section-padding: 80px 0;
        --container-padding: 0 20px;
        --card-padding: 24px;
    }
    
    .hero-section .container {
        grid-template-columns: 1fr;
        gap: 60px;
        text-align: center;
    }
    
    .hero-visual {
        height: 300px;
    }
    
    .parent-benefits-layout {
        grid-template-columns: 1fr;
        gap: 60px;
    }
    
    .coverage-card {
        grid-column: span 1;
    }
    
    .benefits-grid {
        grid-template-columns: 1fr;
        gap: 32px;
    }
    
    .tech-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 24px;
    }
}

@media (max-width: 768px) {
    :root {
        --section-padding: 60px 0;
        --container-padding: 0 16px;
        --card-padding: 20px;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-primary, .btn-secondary, .btn-accent, .btn-success {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .cta-btn {
        width: 100%;
        max-width: 280px;
    }
    
    .floating-card {
        position: static;
        margin-bottom: 16px;
        animation: none;
    }
    
    .hero-visual {
        display: flex;
        flex-direction: column;
        height: auto;
        gap: 16px;
    }
    
    .feature-item {
        flex-direction: column;
        text-align: center;
    }
    
    .feature-icon {
        margin: 0 auto;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 1.75rem;
    }
    
    .section-title {
        font-size: 1.5rem;
    }
    
    .benefits-grid {
        grid-template-columns: 1fr;
    }
    
    .tech-grid {
        grid-template-columns: 1fr;
    }
    
    .chat-container {
        width: 95%;
        margin: 20px;
    }

    .chat-demo-container {
        grid-template-columns: 1fr;
        gap: 24px;
    }

    .chat-features {
        flex-direction: row;
        overflow-x: auto;
        gap: 16px;
    }

    .feature-highlight {
        min-width: 200px;
        flex-shrink: 0;
    }

    .floating-chat-btn {
        bottom: 16px;
        right: 16px;
    }

    .chat-btn-text {
        display: none;
    }

    .chat-btn-content {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        padding: 0;
        justify-content: center;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus styles for keyboard navigation */
.btn-primary:focus,
.btn-secondary:focus,
.btn-accent:focus,
.btn-success:focus,
.chat-input:focus,
.send-btn:focus,
.close-btn:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .glass-card {
        border: 2px solid #000;
        background: rgba(255, 255, 255, 0.9);
    }
    
    .btn-primary, .btn-secondary, .btn-accent, .btn-success {
        border: 2px solid #000;
    }
}

/* Print styles */
@media print {
    .hero-section,
    .cta-section,
    .chat-interface {
        display: none;
    }
    
    .glass-card {
        background: white;
        border: 1px solid #000;
        box-shadow: none;
        backdrop-filter: none;
    }
} 