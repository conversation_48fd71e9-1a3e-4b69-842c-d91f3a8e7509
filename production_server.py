#!/usr/bin/env python3
"""
Production Virat Gyankosh Educational RAG Server
Fully integrated with Pinecone cloud vector database and real AI responses
Mobile-ready with React Native/Expo support
"""

import uvicorn
import os
import logging
import time
from typing import Dict, Any, List, Optional
from dotenv import load_dotenv

from fastapi import FastAPI, HTTPException, Form, File, UploadFile, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse
from pydantic import BaseModel, Field

from agentic_rag_service import AgenticRAGService

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Pydantic models
class QueryRequest(BaseModel):
    query: str = Field(..., description="The user's question")
    device_info: Optional[Dict[str, Any]] = Field(default=None, description="Mobile device information")

class QueryResponse(BaseModel):
    answer: str = Field(..., description="AI-generated answer")
    sources: List[Dict[str, Any]] = Field(default_factory=list, description="Source documents")
    confidence: float = Field(..., description="Response confidence score")
    processing_time: float = Field(..., description="Time taken to process")
    question_type: str = Field(..., description="Classification of question type")
    mobile_optimized: bool = Field(default=True, description="Whether response is mobile-optimized")

class HealthResponse(BaseModel):
    status: str = Field(..., description="System health status")
    components: Dict[str, Any] = Field(..., description="Component health details")

# Initialize FastAPI app
app = FastAPI(
    title="Virat Gyankosh - Production RAG System",
    description="AI-Powered CBSE Learning Assistant with Cloud Vector Database",
    version="3.0.0"
)

# Add CORS middleware with mobile-specific configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:19000",  # Expo development
        "http://localhost:19006",  # Expo web
        "exp://localhost:19000",   # Expo Go app
        "*"                        # Production domains (update for security)
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=[
        "Content-Type",
        "Authorization",
        "X-Device-Type",
        "X-App-Version",
        "*"
    ],
)

# Mount static files
app.mount("/static", StaticFiles(directory="src/static", html=True), name="static")

# Initialize RAG service
rag_service = None
try:
    logger.info("🚀 Initializing Production RAG System with Pinecone...")
    rag_service = AgenticRAGService()
    logger.info("✅ Production RAG System ready!")
    logger.info("☁️ Connected to Pinecone cloud vector database")
    logger.info("🤖 OpenAI GPT integration active")
    logger.info("📚 CBSE textbook knowledge base loaded")
except Exception as e:
    logger.error(f"❌ Failed to initialize RAG service: {e}")
    rag_service = None

@app.get("/")
async def read_root():
    """Serve the main application page."""
    try:
        return FileResponse("src/static/index.html")
    except Exception as e:
        logger.error(f"Error serving index page: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to serve application page")

@app.post("/query", response_model=QueryResponse)
async def query_knowledge_base(
    query: str = Form(...),
    file: UploadFile = File(None)
) -> QueryResponse:
    """Process educational queries using real AI and cloud vector database."""
    start_time = time.time()
    
    try:
        # Validate RAG service availability
        if not rag_service:
            raise HTTPException(
                status_code=503, 
                detail="RAG service not available. Please check API keys and configuration."
            )
        
        # Validate query
        if not query.strip():
            raise HTTPException(status_code=400, detail="Query cannot be empty")
        
        # Handle file upload if present
        if file:
            file_content = await file.read()
            logger.info(f"📎 File uploaded: {file.filename} ({len(file_content)} bytes)")
        
        logger.info(f"🔍 Processing educational query: {query[:50]}...")
        
        # Process through production RAG pipeline
        result = rag_service.process_query(query)
        
        # Calculate processing time
        processing_time = time.time() - start_time
        
        # Format response
        response = QueryResponse(
            answer=result.get("answer", "I couldn't generate a response. Please try again."),
            sources=result.get("sources", []),
            confidence=result.get("confidence", 0.0),
            processing_time=processing_time,
            question_type=result.get("question_type", "educational")
        )
        
        logger.info(f"✅ Query processed successfully in {processing_time:.2f}s")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing query: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Failed to process query. Please try again."
        )

@app.post("/query-json", response_model=QueryResponse)
async def query_knowledge_base_json(request: QueryRequest) -> QueryResponse:
    """Process educational queries via JSON API."""
    start_time = time.time()
    
    try:
        # Validate RAG service availability
        if not rag_service:
            raise HTTPException(
                status_code=503, 
                detail="RAG service not available. Please check API keys and configuration."
            )
        
        # Validate query
        if not request.query.strip():
            raise HTTPException(status_code=400, detail="Query cannot be empty")
        
        logger.info(f"🔍 Processing JSON query: {request.query[:50]}...")
        
        # Process through production RAG pipeline
        result = rag_service.process_query(request.query)
        
        # Calculate processing time
        processing_time = time.time() - start_time
        
        # Format response
        response = QueryResponse(
            answer=result.get("answer", "I couldn't generate a response. Please try again."),
            sources=result.get("sources", []),
            confidence=result.get("confidence", 0.0),
            processing_time=processing_time,
            question_type=result.get("question_type", "educational")
        )
        
        logger.info(f"✅ JSON query processed successfully in {processing_time:.2f}s")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing JSON query: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Failed to process query. Please try again."
        )

@app.get("/health", response_model=HealthResponse)
async def health_check() -> HealthResponse:
    """Check system health status."""
    try:
        if rag_service:
            health_status = rag_service.health_check()
            return HealthResponse(
                status=health_status.get("status", "healthy"),
                components=health_status.get("components", {})
            )
        else:
            return HealthResponse(
                status="unhealthy",
                components={
                    "rag_service": False,
                    "pinecone": False,
                    "openai": False,
                    "embedding_model": False
                }
            )
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return HealthResponse(
            status="degraded",
            components={"error": str(e)}
        )

@app.get("/stats")
async def get_system_stats():
    """Get production system statistics."""
    try:
        if not rag_service:
            raise HTTPException(status_code=503, detail="RAG service not available")
        
        # Get real statistics from Pinecone
        index_stats = rag_service.index.describe_index_stats()
        
        return {
            "system_type": "Production RAG with Pinecone Cloud Database",
            "vector_database": "Pinecone",
            "total_vectors": index_stats.get('total_vector_count', 0),
            "index_fullness": index_stats.get('index_fullness', 0.0),
            "dimensions": index_stats.get('dimension', 384),
            "subjects_covered": ["Geography", "History", "Economics", "Political Science"],
            "textbooks": [
                "Contemporary India-I (Geography)",
                "India and Contemporary World-I (History)", 
                "Understanding Economic Development (Economics)",
                "Democratic Politics-I (Political Science)"
            ],
            "ai_model": "OpenAI GPT-3.5-Turbo",
            "embedding_model": "all-MiniLM-L6-v2",
            "status": "production"
        }
        
    except Exception as e:
        logger.error(f"Error getting stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get system statistics")

@app.get("/subjects")
async def get_subjects():
    """Get available subjects from the knowledge base."""
    return {
        "subjects": [
            {
                "name": "Geography",
                "textbook": "Contemporary India-I",
                "topics": ["Physical Features", "Drainage", "Climate", "Natural Vegetation", "Wildlife", "Population"]
            },
            {
                "name": "History", 
                "textbook": "India and Contemporary World-I",
                "topics": ["French Revolution", "Socialism in Europe", "Nazism", "Forest Society", "Pastoralists"]
            },
            {
                "name": "Economics",
                "textbook": "Understanding Economic Development", 
                "topics": ["Development", "Food Security", "Agriculture", "Globalization"]
            },
            {
                "name": "Political Science",
                "textbook": "Democratic Politics-I",
                "topics": ["Democracy", "Electoral Politics", "Working of Institutions", "Diversity"]
            }
        ],
        "total_concepts": "1837+",
        "database_type": "cloud"
    }

@app.post("/mobile/query", response_model=QueryResponse)
async def mobile_query(request: QueryRequest) -> QueryResponse:
    """Mobile-optimized endpoint for educational queries."""
    start_time = time.time()
    
    try:
        if not rag_service:
            raise HTTPException(
                status_code=503, 
                detail="RAG service not available"
            )
        
        if not request.query.strip():
            raise HTTPException(status_code=400, detail="Query cannot be empty")
        
        # Log device info if provided
        if request.device_info:
            logger.info(f"📱 Mobile query from device: {request.device_info}")
        
        # Process through production RAG pipeline
        result = rag_service.process_query(request.query)
        
        processing_time = time.time() - start_time
        
        response = QueryResponse(
            answer=result.get("answer", "I couldn't generate a response. Please try again."),
            sources=result.get("sources", []),
            confidence=result.get("confidence", 0.0),
            processing_time=processing_time,
            question_type=result.get("question_type", "educational"),
            mobile_optimized=True
        )
        
        logger.info(f"✅ Mobile query processed in {processing_time:.2f}s")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing mobile query: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Failed to process query"
        )

@app.get("/mobile/config")
async def get_mobile_config():
    """Get mobile app configuration settings."""
    return {
        "api_version": "3.0.0",
        "features": {
            "voice_input": True,
            "offline_mode": False,
            "image_upload": True
        },
        "max_query_length": 500,
        "supported_languages": ["en"],
        "cache_ttl": 3600
    }

if __name__ == "__main__":
    # Validate environment variables
    required_vars = ["OPENAI_API_KEY", "PINECONE_API_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"❌ Missing required environment variables: {missing_vars}")
        logger.error("Please set these in your .env file")
        exit(1)
    
    logger.info("🚀 Starting Production Virat Gyankosh Server")
    logger.info("☁️ Pinecone Cloud Vector Database")
    logger.info("🤖 OpenAI GPT Integration")
    logger.info("🌐 Server: http://localhost:8000")
    
    uvicorn.run(
        "production_server:app",
        host="127.0.0.1",
        port=8000,
        reload=False,
        log_level="info"
    ) 