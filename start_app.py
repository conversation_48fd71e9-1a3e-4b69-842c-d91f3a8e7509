#!/usr/bin/env python3
"""
Quick start script for Virat Gyankosh - Lightweight Educational RAG App
"""

import os
import sys
import webbrowser
import time
import threading
import subprocess

def check_requirements():
    """Check if all requirements are installed."""
    try:
        import uvicorn
        import fastapi
        import sentence_transformers
        import openai
        print("✅ All required packages are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing package: {e}")
        print("💡 Run: pip install -r requirements.txt")
        return False

def check_data():
    """Check if CBSE data is available."""
    chunks_dir = "processed_chunks"
    if not os.path.exists(chunks_dir):
        print(f"❌ Data directory not found: {chunks_dir}")
        return False
    
    chunk_files = [f for f in os.listdir(chunks_dir) if f.endswith('.json')]
    if len(chunk_files) < 4:
        print(f"❌ Incomplete data: found {len(chunk_files)} files, expected 4")
        return False
    
    print(f"✅ CBSE data ready: {len(chunk_files)} subject files")
    return True

def check_config():
    """Check if configuration is set up."""
    if not os.path.exists('.env'):
        print("❌ Configuration file (.env) not found")
        return False
    
    with open('.env', 'r') as f:
        content = f.read()
    
    if 'OPENAI_API_KEY=sk-' not in content:
        print("⚠️ OpenAI API key may not be configured")
    else:
        print("✅ OpenAI API key configured")
    
    if 'PINECONE_API_KEY=pcsk_' not in content:
        print("⚠️ Pinecone API key may not be configured")
    else:
        print("✅ Pinecone API key configured")
    
    return True

def open_browser_delayed():
    """Open browser after server starts."""
    time.sleep(3)
    webbrowser.open("http://localhost:8001")

def main():
    """Main startup function."""
    print("🚀 VIRAT GYANKOSH - LIGHTWEIGHT STARTUP")
    print("=" * 50)
    print("AI-Powered CBSE Class 9 Learning Assistant")
    print("Enhanced UI/UX with Real Textbook Data")
    print()
    
    # Check system requirements
    print("🔍 Checking system requirements...")
    
    if not check_requirements():
        print("\n❌ Requirements check failed")
        return False
    
    if not check_data():
        print("\n❌ Data check failed")
        return False
    
    if not check_config():
        print("\n⚠️ Configuration check completed with warnings")
    
    print("\n✅ All checks passed!")
    print("\n🎯 Starting application...")
    print("   • Professional landing page")
    print("   • Enhanced chat interface")
    print("   • Real AI responses from CBSE textbooks")
    print("   • 1,837 concepts across 4 subjects")
    print("   • Modern responsive design")
    print()
    
    # Start browser in background
    browser_thread = threading.Thread(target=open_browser_delayed)
    browser_thread.daemon = True
    browser_thread.start()
    
    print("🌐 Server will start at: http://localhost:8001")
    print("🔄 Browser will open automatically...")
    print("⏹️ Press Ctrl+C to stop the server")
    print()
    
    try:
        # Start the production server with Pinecone integration
        subprocess.run([sys.executable, "production_server.py"])
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💡 Troubleshooting:")
        print("   1. Install requirements: pip install -r requirements.txt")
        print("   2. Check .env file for API keys")
        print("   3. Ensure processed_chunks/ directory exists")
        print("   4. Try running: python simple_demo_server.py")
        sys.exit(1)
