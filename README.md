# Virat Gyankosh - CBSE 9th Grade Learning Assistant

An intelligent RAG (Retrieval-Augmented Generation) system designed to help students with their CBSE 9th grade studies. The system provides accurate, contextual answers by referencing official CBSE textbooks.

## Features

- **Real-time Question Answering**: Get instant, accurate answers to your questions about CBSE 9th grade subjects
- **Source Attribution**: Every answer comes with references to the specific textbook sections
- **High Coverage**: Comprehensive coverage across subjects:
  - Geography: 92.6%
  - Economics: 96.9%
  - History: 98.4%
  - Political Science: 99.0%
- **Modern Web Interface**: Clean, responsive design with real-time status monitoring
- **Smart Retrieval**: Uses advanced embedding and semantic search for accurate information retrieval

## Technical Stack

- **Backend**:
  - FastAPI for the web server
  - SentenceTransformer for text embeddings
  - Pinecone for vector storage
  - OpenAI GPT-3.5 for answer generation
- **Frontend**:
  - Modern UI with TailwindCSS
  - Real-time updates and status monitoring
  - Responsive design with glassmorphism effects

## Setup

1. **Environment Setup**:
   ```bash
   # Create and activate virtual environment
   python -m venv venv
   source venv/bin/activate  # On Windows: .\venv\Scripts\activate
   
   # Install dependencies
   pip install -r requirements.txt
   ```

2. **Environment Variables**:
   Create a `.env` file in the root directory with:
   ```
   PINECONE_API_KEY=your_pinecone_api_key
   OPENAI_API_KEY=your_openai_api_key
   ```

3. **Running the Server**:
   ```bash
   python run_server.py
   ```
   The server will start at `http://localhost:8000`

## Project Structure

```
.
├── src/
│   ├── core/
│   │   └── config.py
│   ├── models/
│   │   └── __init__.py
│   ├── services/
│   │   └── __init__.py
│   └── static/
│       ├── index.html
│       └── app.js
├── requirements.txt
├── run_server.py
└── README.md
```

## API Endpoints

- `GET /`: Main web interface
- `POST /query`: Submit a question
  ```json
  {
    "query": "What is democracy?"
  }
  ```
- `GET /health`: Check system status

## Performance Optimization

- Chunk size: 256 tokens (optimized from 512)
- Chunk overlap: 128 tokens (adjusted from 50)
- Total vectors: 3,111 (increased from 1,535)

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details. 