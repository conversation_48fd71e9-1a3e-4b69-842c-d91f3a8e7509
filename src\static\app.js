// Futuristic Educational RAG App - JavaScript

// DOM Elements - Will be initialized after DOM loads
let getStartedBtn, startChatBtn, floatingChatBtn, watchDemoBtn, chatInterface, closeChatBtn;
let chatSidebar, toggleSidebarBtn, newChatBtn, chatSearchInput, chatHistoryList, clearHistoryBtn;
let themeToggleBtn, minimizeChatBtn, maximizeChatBtn;
let chatMessages, queryForm, queryInput;
let attachBtn, fileInput, filePreview, voiceBtn, voiceRecording, emojiBtn, emojiPicker, emojiGrid;

// Global Variables
let currentTheme = localStorage.getItem('chat-theme') || 'light';
let isRecording = false;
let mediaRecorder = null;
let recordingTimer = null;
let recordingStartTime = 0;
let chatHistory = JSON.parse(localStorage.getItem('chat-history')) || [];
let currentChatId = null;
let sidebarCollapsed = false;

// Message Templates
const createUserMessage = (text) => `
    <div class="flex justify-end mb-4">
        <div class="bg-blue-600 text-white rounded-lg py-2 px-4 max-w-[70%]">
            ${escapeHtml(text)}
        </div>
    </div>
`;

const createAssistantMessage = (response) => `
    <div class="flex justify-start mb-4">
        <div class="bg-white rounded-lg py-2 px-4 max-w-[70%] shadow">
            <div class="prose">
                ${escapeHtml(response.answer)}
            </div>
            ${response.sources.length > 0 ? createSourcesSection(response.sources) : ''}
            <div class="mt-2 text-sm text-gray-500">
                Confidence: ${(response.confidence * 100).toFixed(1)}%
            </div>
        </div>
    </div>
`;

const createSourcesSection = (sources) => `
    <div class="mt-2 text-sm text-gray-600">
        <div class="font-semibold">Sources:</div>
        <ul class="list-disc list-inside">
            ${sources.map(source => `
                <li>${escapeHtml(source.source)} (Relevance: ${(source.score * 100).toFixed(1)}%)</li>
            `).join('')}
        </ul>
    </div>
`;

const createStatusIndicator = (name, status) => `
    <div class="text-center">
        <div class="text-sm font-medium">${escapeHtml(name)}</div>
        <div class="mt-1">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                status ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }">
                ${status ? 'Healthy' : 'Unhealthy'}
            </span>
        </div>
    </div>
`;

// Theme Management System
class ThemeManager {
    constructor() {
        this.currentTheme = localStorage.getItem('chat-theme') || 'light';
        this.init();
    }

    init() {
        this.applyTheme(this.currentTheme);
        this.setupThemeToggle();
    }

    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        this.currentTheme = theme;
        localStorage.setItem('chat-theme', theme);

        // Update theme toggle icon
        if (themeToggleBtn) {
            const icon = themeToggleBtn.querySelector('i');
            if (theme === 'dark') {
                icon.className = 'fas fa-sun';
                themeToggleBtn.classList.add('dark-mode');
            } else {
                icon.className = 'fas fa-moon';
                themeToggleBtn.classList.remove('dark-mode');
            }
        }
    }

    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(newTheme);

        // Add transition effect
        document.body.style.transition = 'all 0.3s ease';
        setTimeout(() => {
            document.body.style.transition = '';
        }, 300);
    }

    setupThemeToggle() {
        if (themeToggleBtn) {
            themeToggleBtn.addEventListener('click', () => this.toggleTheme());
        }
    }
}

// Chat History Management
class ChatHistoryManager {
    constructor() {
        this.history = JSON.parse(localStorage.getItem('chat-history')) || [];
        this.currentChatId = null;
        this.init();
    }

    init() {
        this.renderHistory();
        this.setupEventListeners();
    }

    setupEventListeners() {
        if (newChatBtn) {
            newChatBtn.addEventListener('click', () => this.createNewChat());
        }

        if (clearHistoryBtn) {
            clearHistoryBtn.addEventListener('click', () => this.clearHistory());
        }

        if (chatSearchInput) {
            chatSearchInput.addEventListener('input', (e) => this.searchHistory(e.target.value));
        }
    }

    createNewChat() {
        const chatId = 'chat_' + Date.now();
        const newChat = {
            id: chatId,
            title: 'New Conversation',
            messages: [],
            timestamp: Date.now(),
            preview: 'Start a new conversation...'
        };

        this.history.unshift(newChat);
        this.currentChatId = chatId;
        this.saveHistory();
        this.renderHistory();
        this.clearMessages();

        // Add welcome message
        if (window.chatController) {
            window.chatController.addWelcomeMessage();
        }
    }

    saveHistory() {
        localStorage.setItem('chat-history', JSON.stringify(this.history));
    }

    clearHistory() {
        if (confirm('Are you sure you want to clear all chat history?')) {
            this.history = [];
            this.currentChatId = null;
            this.saveHistory();
            this.renderHistory();
            this.clearMessages();
        }
    }

    clearMessages() {
        if (chatMessages) {
            chatMessages.innerHTML = '';
        }
    }

    addMessageToHistory(type, content) {
        if (!this.currentChatId) {
            this.createNewChat();
        }

        const chat = this.history.find(c => c.id === this.currentChatId);
        if (chat) {
            chat.messages.push({
                type,
                content,
                timestamp: Date.now()
            });

            // Update chat title and preview
            if (type === 'user' && chat.title === 'New Conversation') {
                chat.title = content.substring(0, 30) + (content.length > 30 ? '...' : '');
            }

            if (type === 'user') {
                chat.preview = content.substring(0, 50) + (content.length > 50 ? '...' : '');
            }

            chat.timestamp = Date.now();
            this.saveHistory();
            this.renderHistory();
        }
    }

    loadChat(chatId) {
        const chat = this.history.find(c => c.id === chatId);
        if (chat) {
            this.currentChatId = chatId;
            this.clearMessages();

            // Render all messages
            chat.messages.forEach(msg => {
                if (window.chatController) {
                    window.chatController.addMessage(msg.type, msg.content, false);
                }
            });

            this.renderHistory();
        }
    }

    searchHistory(query) {
        const filtered = this.history.filter(chat =>
            chat.title.toLowerCase().includes(query.toLowerCase()) ||
            chat.preview.toLowerCase().includes(query.toLowerCase())
        );
        this.renderHistory(filtered);
    }

    renderHistory(historyToRender = null) {
        if (!chatHistoryList) return;

        const history = historyToRender || this.history;

        chatHistoryList.innerHTML = history.map(chat => `
            <div class="chat-history-item ${chat.id === this.currentChatId ? 'active' : ''}"
                 data-chat-id="${chat.id}">
                <div class="history-item-title">${this.escapeHtml(chat.title)}</div>
                <div class="history-item-preview">${this.escapeHtml(chat.preview)}</div>
                <div class="history-item-time">${this.formatTime(chat.timestamp)}</div>
            </div>
        `).join('');

        // Add click listeners
        chatHistoryList.querySelectorAll('.chat-history-item').forEach(item => {
            item.addEventListener('click', () => {
                const chatId = item.dataset.chatId;
                this.loadChat(chatId);
            });
        });
    }

    formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;

        if (diff < 24 * 60 * 60 * 1000) {
            return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        } else if (diff < 7 * 24 * 60 * 60 * 1000) {
            return date.toLocaleDateString([], { weekday: 'short' });
        } else {
            return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
        }
    }

    escapeHtml(unsafe) {
        if (!unsafe) return '';
        return unsafe
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }
}

// Utility Functions
function escapeHtml(unsafe) {
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

function scrollToBottom() {
    if (chatMessages) {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
}

// File Attachment Manager
class FileAttachmentManager {
    constructor() {
        this.currentFile = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        if (attachBtn) {
            attachBtn.addEventListener('click', () => this.openFileDialog());
        }

        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        }

        // Remove file button
        document.addEventListener('click', (e) => {
            if (e.target.closest('.remove-file-btn')) {
                this.removeFile();
            }
        });
    }

    openFileDialog() {
        if (fileInput) {
            fileInput.click();
        }
    }

    handleFileSelect(event) {
        const file = event.target.files[0];
        if (file) {
            this.currentFile = file;
            this.showFilePreview(file);
        }
    }

    showFilePreview(file) {
        if (!filePreview) return;

        const fileName = file.name;
        const fileSize = this.formatFileSize(file.size);

        filePreview.querySelector('.file-name').textContent = fileName;
        filePreview.querySelector('.file-size').textContent = fileSize;
        filePreview.classList.remove('hidden');
    }

    removeFile() {
        this.currentFile = null;
        if (filePreview) {
            filePreview.classList.add('hidden');
        }
        if (fileInput) {
            fileInput.value = '';
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    getAttachedFile() {
        return this.currentFile;
    }
}

// Voice Recording Manager
class VoiceRecordingManager {
    constructor() {
        this.isRecording = false;
        this.mediaRecorder = null;
        this.recordingTimer = null;
        this.recordingStartTime = 0;
        this.audioChunks = [];
        this.init();
    }

    init() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        if (voiceBtn) {
            voiceBtn.addEventListener('click', () => this.toggleRecording());
        }

        // Stop recording when clicking on recording indicator
        if (voiceRecording) {
            voiceRecording.addEventListener('click', () => this.stopRecording());
        }
    }

    async toggleRecording() {
        if (this.isRecording) {
            this.stopRecording();
        } else {
            await this.startRecording();
        }
    }

    async startRecording() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

            this.mediaRecorder = new MediaRecorder(stream);
            this.audioChunks = [];

            this.mediaRecorder.ondataavailable = (event) => {
                this.audioChunks.push(event.data);
            };

            this.mediaRecorder.onstop = () => {
                this.processRecording();
            };

            this.mediaRecorder.start();
            this.isRecording = true;
            this.recordingStartTime = Date.now();

            this.showRecordingUI();
            this.startTimer();

        } catch (error) {
            console.error('Error accessing microphone:', error);
            alert('Unable to access microphone. Please check permissions.');
        }
    }

    stopRecording() {
        if (this.mediaRecorder && this.isRecording) {
            this.mediaRecorder.stop();
            this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
            this.isRecording = false;
            this.hideRecordingUI();
            this.stopTimer();
        }
    }

    showRecordingUI() {
        if (voiceRecording) {
            voiceRecording.classList.remove('hidden');
        }
        if (voiceBtn) {
            voiceBtn.classList.add('recording');
        }
    }

    hideRecordingUI() {
        if (voiceRecording) {
            voiceRecording.classList.add('hidden');
        }
        if (voiceBtn) {
            voiceBtn.classList.remove('recording');
        }
    }

    startTimer() {
        this.recordingTimer = setInterval(() => {
            const elapsed = Date.now() - this.recordingStartTime;
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

            const timerElement = voiceRecording?.querySelector('.recording-timer');
            if (timerElement) {
                timerElement.textContent = timeString;
            }
        }, 1000);
    }

    stopTimer() {
        if (this.recordingTimer) {
            clearInterval(this.recordingTimer);
            this.recordingTimer = null;
        }
    }

    processRecording() {
        const audioBlob = new Blob(this.audioChunks, { type: 'audio/wav' });

        // For now, we'll convert to text using Web Speech API
        // In a real implementation, you'd send this to a speech-to-text service
        this.convertSpeechToText(audioBlob);
    }

    convertSpeechToText(audioBlob) {
        // This is a simplified implementation
        // In production, you'd use a proper speech-to-text service
        const recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();

        if (recognition) {
            recognition.continuous = false;
            recognition.interimResults = false;
            recognition.lang = 'en-US';

            recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                if (queryInput) {
                    queryInput.value = transcript;
                    queryInput.focus();
                }
            };

            recognition.onerror = (event) => {
                console.error('Speech recognition error:', event.error);
            };

            // Note: This is a simplified approach
            // Real implementation would process the audioBlob
            recognition.start();
        } else {
            alert('Speech recognition not supported in this browser.');
        }
    }
}

// Emoji Picker Manager
class EmojiPickerManager {
    constructor() {
        this.isOpen = false;
        this.emojis = {
            smileys: ['😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂', '🙂', '🙃', '😉', '😊', '😇', '🥰', '😍', '🤩', '😘', '😗', '😚', '😙'],
            people: ['👋', '🤚', '🖐', '✋', '🖖', '👌', '🤏', '✌', '🤞', '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇', '☝', '👍', '👎'],
            nature: ['🌟', '⭐', '🌙', '☀', '⛅', '🌤', '⛈', '🌧', '❄', '☃', '⛄', '🌈', '🔥', '💧', '🌊', '🎄', '🌲', '🌳', '🌴', '🌱'],
            objects: ['📚', '📖', '📝', '✏', '🖊', '🖋', '🖌', '🖍', '📄', '📃', '📑', '📊', '📈', '📉', '📇', '🗃', '🗄', '🗂', '📂', '📁']
        };
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.renderEmojis('smileys');
    }

    setupEventListeners() {
        if (emojiBtn) {
            emojiBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.togglePicker();
            });
        }

        document.addEventListener('click', (e) => {
            if (!e.target.closest('#emoji-picker') && !e.target.closest('#emoji-btn')) {
                this.closePicker();
            }
        });

        document.addEventListener('click', (e) => {
            if (e.target.closest('.emoji-category')) {
                const category = e.target.closest('.emoji-category').dataset.category;
                this.switchCategory(category);
            }
        });

        document.addEventListener('click', (e) => {
            if (e.target.closest('.emoji-item')) {
                const emoji = e.target.closest('.emoji-item').textContent;
                this.insertEmoji(emoji);
            }
        });
    }

    togglePicker() {
        if (this.isOpen) {
            this.closePicker();
        } else {
            this.openPicker();
        }
    }

    openPicker() {
        if (emojiPicker) {
            emojiPicker.classList.remove('hidden');
            this.isOpen = true;
        }
    }

    closePicker() {
        if (emojiPicker) {
            emojiPicker.classList.add('hidden');
            this.isOpen = false;
        }
    }

    switchCategory(category) {
        document.querySelectorAll('.emoji-category').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-category="${category}"]`)?.classList.add('active');
        this.renderEmojis(category);
    }

    renderEmojis(category) {
        if (!emojiGrid || !this.emojis[category]) return;
        emojiGrid.innerHTML = this.emojis[category].map(emoji =>
            `<button class="emoji-item" type="button">${emoji}</button>`
        ).join('');
    }

    insertEmoji(emoji) {
        if (queryInput) {
            const cursorPos = queryInput.selectionStart;
            const textBefore = queryInput.value.substring(0, cursorPos);
            const textAfter = queryInput.value.substring(queryInput.selectionEnd);
            queryInput.value = textBefore + emoji + textAfter;
            queryInput.focus();
            queryInput.setSelectionRange(cursorPos + emoji.length, cursorPos + emoji.length);
        }
        this.closePicker();
    }
}

// Sidebar Manager
class SidebarManager {
    constructor() {
        this.isCollapsed = false;
        this.init();
    }

    init() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        if (toggleSidebarBtn) {
            toggleSidebarBtn.addEventListener('click', () => this.toggleSidebar());
        }
    }

    toggleSidebar() {
        if (chatSidebar) {
            if (this.isCollapsed) {
                chatSidebar.classList.remove('collapsed');
                chatSidebar.classList.add('open');
            } else {
                chatSidebar.classList.add('collapsed');
                chatSidebar.classList.remove('open');
            }
            this.isCollapsed = !this.isCollapsed;
        }
    }
}

// Animation and Interaction Controllers
class AnimationController {
    constructor() {
        this.observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        this.init();
    }

    init() {
        this.setupIntersectionObserver();
        this.setupScrollAnimations();
        this.setupHoverEffects();
        this.setupCounterAnimations();
    }

    setupIntersectionObserver() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in');
                    
                    // Trigger specific animations
                    if (entry.target.classList.contains('tech-card')) {
                        this.animateTechCard(entry.target);
                    }
                    if (entry.target.classList.contains('coverage-fill')) {
                        this.animateProgressBar(entry.target);
                    }
                }
            });
        }, this.observerOptions);

        // Observe elements
        document.querySelectorAll('.glass-card, .benefit-card, .tech-card, .feature-item').forEach(el => {
            observer.observe(el);
        });
    }

    setupScrollAnimations() {
        let lastScrollY = window.scrollY;
        
        window.addEventListener('scroll', () => {
            const currentScrollY = window.scrollY;
            
            // Parallax effect for hero section
            const heroSection = document.querySelector('.hero-section');
            if (heroSection) {
                const scrolled = window.pageYOffset;
                const parallax = scrolled * -0.2;
                heroSection.style.transform = `translateY(${parallax}px)`;
            }

            // Floating cards animation on scroll
            document.querySelectorAll('.floating-card').forEach((card, index) => {
                const offset = (window.scrollY * 0.1 * (index + 1));
                card.style.transform = `translateY(${offset}px)`;
            });

            lastScrollY = currentScrollY;
        });
    }

    setupHoverEffects() {
        // Enhanced hover effects for glass cards
        document.querySelectorAll('.glass-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-12px) scale(1.02)';
                card.style.boxShadow = '0 25px 50px 0 rgba(31, 38, 135, 0.5)';
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0) scale(1)';
                card.style.boxShadow = '0 8px 32px 0 rgba(31, 38, 135, 0.37)';
            });
        });

        // Button hover effects
        document.querySelectorAll('.btn-primary, .btn-secondary, .btn-accent, .btn-success').forEach(btn => {
            btn.addEventListener('mouseenter', () => {
                btn.style.transform = 'translateY(-3px) scale(1.02)';
                this.createRippleEffect(btn);
            });

            btn.addEventListener('mouseleave', () => {
                btn.style.transform = 'translateY(0) scale(1)';
            });
        });
    }

    setupCounterAnimations() {
        const counters = document.querySelectorAll('.tech-stat, .stat-value');
        
        counters.forEach(counter => {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.animateCounter(entry.target);
                        observer.unobserve(entry.target);
                    }
                });
            });
            
            observer.observe(counter);
        });
    }

    animateCounter(element) {
        const text = element.textContent;
        const number = parseFloat(text.replace(/[^\d.]/g, ''));
        
        if (isNaN(number)) return;
        
        const suffix = text.replace(number.toString(), '');
        const duration = 2000;
        const steps = 50;
        const increment = number / steps;
        let current = 0;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= number) {
                current = number;
                clearInterval(timer);
            }
            
            element.textContent = Math.floor(current) + suffix;
        }, duration / steps);
    }

    animateTechCard(card) {
        const icon = card.querySelector('.tech-icon');
        if (icon) {
            icon.style.animation = 'pulse 2s infinite';
        }
    }

    animateProgressBar(bar) {
        const width = bar.style.width || '0%';
        bar.style.width = '0%';
        
        setTimeout(() => {
            bar.style.transition = 'width 2s ease-out';
            bar.style.width = width;
        }, 200);
    }

    createRippleEffect(element) {
        const ripple = document.createElement('span');
        ripple.classList.add('ripple-effect');
        ripple.style.cssText = `
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.4);
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
            width: 20px;
            height: 20px;
            left: 50%;
            top: 50%;
            margin-left: -10px;
            margin-top: -10px;
        `;
        
        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    }
}

// Modern Chat Interface Controller
class ChatController {
    constructor() {
        this.messages = [];
        this.isOpen = false;
        this.isMinimized = false;
        this.isMaximized = false;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupMessageTemplates();
        this.setupQuickActions();
    }

    setupEventListeners() {
        console.log('🔧 ChatController: Setting up event listeners...');
        
        // Open chat interface - multiple buttons
        if (getStartedBtn) {
            console.log('✅ Adding click listener to getStartedBtn');
            getStartedBtn.addEventListener('click', (e) => {
                console.log('🔴 getStartedBtn clicked!');
                e.preventDefault();
                this.openChat();
            });
        } else {
            console.error('❌ getStartedBtn not found during event listener setup');
        }

        if (startChatBtn) {
            console.log('✅ Adding click listener to startChatBtn');
            startChatBtn.addEventListener('click', (e) => {
                console.log('🔴 startChatBtn clicked!');
                e.preventDefault();
                this.openChat();
            });
        } else {
            console.error('❌ startChatBtn not found during event listener setup');
        }

        if (floatingChatBtn) {
            console.log('✅ Adding click listener to floatingChatBtn');
            floatingChatBtn.addEventListener('click', (e) => {
                console.log('🔴 floatingChatBtn clicked!');
                e.preventDefault();
                this.openChat();
            });
        } else {
            console.error('❌ floatingChatBtn not found during event listener setup');
        }

        // Close chat interface
        if (closeChatBtn) {
            closeChatBtn.addEventListener('click', () => this.closeChat());
        }

        // Minimize/Maximize controls
        if (minimizeChatBtn) {
            minimizeChatBtn.addEventListener('click', () => this.minimizeChat());
        }

        if (maximizeChatBtn) {
            maximizeChatBtn.addEventListener('click', () => this.maximizeChat());
        }

        // Handle form submission
        if (queryForm) {
            queryForm.addEventListener('submit', (e) => this.handleSubmit(e));
        }

        // Close chat on outside click
        if (chatInterface) {
            chatInterface.addEventListener('click', (e) => {
                if (e.target === chatInterface) {
                    this.closeChat();
                }
            });
        }

        // Handle demo button
        if (watchDemoBtn) {
            watchDemoBtn.addEventListener('click', () => this.showDemo());
        }

        // Message actions
        document.addEventListener('click', (e) => {
            if (e.target.closest('.message-action-btn')) {
                const btn = e.target.closest('.message-action-btn');
                const action = btn.title.toLowerCase();
                this.handleMessageAction(action, btn);
            }
        });
    }

    setupQuickActions() {
        document.addEventListener('click', (e) => {
            if (e.target.closest('.quick-action-btn')) {
                const btn = e.target.closest('.quick-action-btn');
                const action = btn.dataset.action;
                const messageContent = btn.closest('.message-content');

                this.handleQuickAction(action, messageContent);
            }
        });
    }

    handleQuickAction(action, messageContent) {
        const originalText = messageContent.querySelector('div:first-child').textContent;

        switch(action) {
            case 'explain-further':
                if (queryInput) {
                    queryInput.value = `Can you explain this in more detail: "${originalText.substring(0, 100)}..."`;
                    queryInput.focus();
                }
                break;
            case 'show-examples':
                if (queryInput) {
                    queryInput.value = `Can you provide specific examples for: "${originalText.substring(0, 100)}..."`;
                    queryInput.focus();
                }
                break;
            case 'create-quiz':
                this.generateQuiz(originalText, messageContent);
                break;
        }
    }

    generateQuiz(content, container) {
        // Create quiz container
        let quizContainer = container.querySelector('.quiz-container');
        if (!quizContainer) {
            quizContainer = document.createElement('div');
            quizContainer.className = 'quiz-container';
            quizContainer.style.cssText = `
                margin-top: 20px;
                padding: 20px;
                background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
                border-radius: 12px;
                border: 1px solid #f59e0b;
            `;
            container.appendChild(quizContainer);
        }

        // Generate simple quiz questions based on content
        const questions = this.generateQuizQuestions(content);

        quizContainer.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 16px;">
                <i class="fas fa-question-circle" style="color: #f59e0b;"></i>
                <strong>Quick Quiz</strong>
            </div>
            ${questions.map((q, index) => `
                <div class="quiz-question" style="margin-bottom: 16px; padding: 12px; background: white; border-radius: 8px;">
                    <div style="font-weight: 500; margin-bottom: 8px;">${index + 1}. ${q.question}</div>
                    ${q.options.map((option, optIndex) => `
                        <label style="display: block; margin: 4px 0; cursor: pointer;">
                            <input type="radio" name="q${index}" value="${optIndex}" style="margin-right: 8px;">
                            ${option}
                        </label>
                    `).join('')}
                </div>
            `).join('')}
            <button onclick="this.parentElement.querySelector('.quiz-results').style.display='block'; this.style.display='none';"
                    style="background: #f59e0b; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer;">
                Check Answers
            </button>
            <div class="quiz-results" style="display: none; margin-top: 12px; padding: 12px; background: #dcfce7; border-radius: 8px;">
                <strong>Answers:</strong><br>
                ${questions.map((q, index) => `${index + 1}. ${q.options[q.correct]}`).join('<br>')}
            </div>
        `;
    }

    generateQuizQuestions(content) {
        // Simple quiz generation - can be enhanced with AI
        const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 20);
        const questions = [];

        for (let i = 0; i < Math.min(3, sentences.length); i++) {
            const sentence = sentences[i].trim();
            const words = sentence.split(' ');

            if (words.length > 5) {
                // Create a fill-in-the-blank question
                const keyWordIndex = Math.floor(words.length / 2);
                const keyWord = words[keyWordIndex];
                const questionText = sentence.replace(keyWord, '____');

                questions.push({
                    question: questionText + '?',
                    options: [
                        keyWord,
                        'Alternative 1',
                        'Alternative 2',
                        'Alternative 3'
                    ].sort(() => Math.random() - 0.5),
                    correct: 0
                });
            }
        }

        return questions.length > 0 ? questions : [{
            question: 'What is the main topic discussed?',
            options: ['Topic A', 'Topic B', 'Topic C', 'Topic D'],
            correct: 0
        }];
    }

    handleMessageAction(action, btn) {
        const messageContent = btn.closest('.message-content');
        const text = messageContent.querySelector('div:first-child').textContent;

        switch(action) {
            case 'copy':
                navigator.clipboard.writeText(text).then(() => {
                    btn.innerHTML = '<i class="fas fa-check"></i>';
                    setTimeout(() => {
                        btn.innerHTML = '<i class="fas fa-copy"></i>';
                    }, 2000);
                });
                break;
            case 'regenerate':
                // Regenerate last response
                this.regenerateLastResponse();
                break;
            case 'like':
                btn.classList.toggle('liked');
                btn.innerHTML = btn.classList.contains('liked') ?
                    '<i class="fas fa-thumbs-up" style="color: #3b82f6;"></i>' :
                    '<i class="fas fa-thumbs-up"></i>';
                break;
            case 'mind map':
                this.generateMindMap(text, messageContent);
                break;
        }
    }

    generateMindMap(content, container) {
        // Extract key concepts from the content
        const concepts = this.extractConcepts(content);
        const mindMapHtml = this.createMindMapHTML(concepts);

        // Create or update mind map container
        let mindMapContainer = container.querySelector('.mind-map-container');
        if (!mindMapContainer) {
            mindMapContainer = document.createElement('div');
            mindMapContainer.className = 'mind-map-container';
            mindMapContainer.style.cssText = `
                margin-top: 20px;
                padding: 20px;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 12px;
                border: 1px solid #e2e8f0;
            `;
            container.appendChild(mindMapContainer);
        }

        mindMapContainer.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 16px;">
                <i class="fas fa-project-diagram" style="color: #3b82f6;"></i>
                <strong>Concept Mind Map</strong>
            </div>
            ${mindMapHtml}
        `;
    }

    extractConcepts(content) {
        // Simple concept extraction - can be enhanced with NLP
        const sentences = content.split(/[.!?]+/);
        const concepts = [];

        sentences.forEach(sentence => {
            const words = sentence.trim().split(' ');
            if (words.length > 3 && words.length < 15) {
                concepts.push(words.join(' ').trim());
            }
        });

        return concepts.slice(0, 6); // Limit to 6 concepts
    }

    createMindMapHTML(concepts) {
        const centerConcept = concepts[0] || 'Main Topic';
        const subConcepts = concepts.slice(1);

        return `
            <div class="mind-map" style="position: relative; height: 300px; display: flex; align-items: center; justify-content: center;">
                <div class="central-node" style="
                    position: absolute;
                    background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
                    color: white;
                    padding: 16px 20px;
                    border-radius: 50px;
                    font-weight: 600;
                    font-size: 0.9rem;
                    text-align: center;
                    max-width: 150px;
                    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
                    z-index: 10;
                ">${centerConcept}</div>
                ${subConcepts.map((concept, index) => {
                    const angle = (360 / subConcepts.length) * index;
                    const radius = 120;
                    const x = Math.cos(angle * Math.PI / 180) * radius;
                    const y = Math.sin(angle * Math.PI / 180) * radius;

                    return `
                        <div class="concept-node" style="
                            position: absolute;
                            background: white;
                            border: 2px solid #e2e8f0;
                            color: #1e293b;
                            padding: 12px 16px;
                            border-radius: 20px;
                            font-size: 0.8rem;
                            text-align: center;
                            max-width: 120px;
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                            transform: translate(${x}px, ${y}px) translate(-50%, -50%);
                            transition: all 0.2s ease;
                            cursor: pointer;
                        " onmouseover="this.style.transform='translate(${x}px, ${y}px) translate(-50%, -50%) scale(1.05)'"
                           onmouseout="this.style.transform='translate(${x}px, ${y}px) translate(-50%, -50%) scale(1)'"
                        >${concept}</div>
                        <svg style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; z-index: 1;">
                            <line x1="50%" y1="50%"
                                  x2="${50 + (x/3)}%" y2="${50 + (y/3)}%"
                                  stroke="#cbd5e1" stroke-width="2" opacity="0.6"/>
                        </svg>
                    `;
                }).join('')}
            </div>
        `;
    }

    minimizeChat() {
        if (chatInterface) {
            chatInterface.style.transform = 'scale(0.1)';
            chatInterface.style.opacity = '0.5';
            this.isMinimized = true;
        }
    }

    maximizeChat() {
        if (chatInterface) {
            chatInterface.style.transform = 'scale(1)';
            chatInterface.style.opacity = '1';
            this.isMinimized = false;
        }
    }

    setupMessageTemplates() {
        this.templates = {
            user: (text) => `
                <div class="message user-message">
                    <div class="message-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="message-content">
                        ${this.escapeHtml(text)}
                        <div class="message-metadata">
                            You • ${this.getTimestamp()}
                            <div class="message-actions">
                                <button class="message-action-btn" title="Copy">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `,
            assistant: (response) => `
                <div class="message assistant-message">
                    <div class="message-avatar">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div class="message-content">
                        ${this.formatEducationalContent(response.answer)}
                        ${response.sources ? this.createEnhancedSourcesSection(response.sources) : ''}
                        ${this.createQuickActions(response)}
                        <div class="message-metadata">
                            AI Assistant • ${this.getTimestamp()} • Confidence: ${(response.confidence * 100).toFixed(1)}%
                            <div class="message-actions">
                                <button class="message-action-btn" title="Copy">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="message-action-btn" title="Regenerate">
                                    <i class="fas fa-redo"></i>
                                </button>
                                <button class="message-action-btn" title="Like">
                                    <i class="fas fa-thumbs-up"></i>
                                </button>
                                <button class="message-action-btn" title="Mind Map">
                                    <i class="fas fa-project-diagram"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `,
            loading: () => `
                <div class="typing-indicator">
                    <div class="typing-avatar">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                    <div class="typing-text">AI is thinking...</div>
                </div>
            `,
            error: (error) => `
                <div class="message assistant-message error-message">
                    <div class="message-avatar">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="message-content">
                        <strong>⚠️ Error:</strong> ${this.escapeHtml(error)}
                        <div class="message-metadata">Error • ${this.getTimestamp()}</div>
                    </div>
                </div>
            `
        };
    }

    openChat() {
        console.log('🚀 ChatController.openChat() called');
        console.log('- chatInterface exists:', !!chatInterface);
        
        if (chatInterface) {
            console.log('✅ Opening chat interface...');
            chatInterface.classList.remove('hidden');
            chatInterface.classList.add('active');
            document.body.style.overflow = 'hidden';
            this.isOpen = true;
            
            console.log('✅ Chat interface opened successfully');
            
            // Focus on input
            setTimeout(() => {
                if (queryInput) {
                    queryInput.focus();
                    console.log('✅ Query input focused');
                } else {
                    console.log('❌ Query input not found for focus');
                }
            }, 300);
            
            // Add welcome message if first time
            if (this.messages.length === 0) {
                console.log('🎉 Adding welcome message...');
                this.addWelcomeMessage();
            }
        } else {
            console.error('❌ CRITICAL: chatInterface element not found! Cannot open chat.');
        }
    }

    closeChat() {
        if (chatInterface) {
            chatInterface.classList.remove('active');
            document.body.style.overflow = '';
            this.isOpen = false;
            
            setTimeout(() => {
                chatInterface.classList.add('hidden');
            }, 300);
        }
    }

    addWelcomeMessage() {
        const welcomeMessage = {
            answer: "Hello! I'm your AI study assistant. I can help you with questions about History, Political Science, Geography, and more. What would you like to learn today?",
            confidence: 1.0,
            sources: []
        };
        
        if (chatMessages) {
            chatMessages.innerHTML = this.templates.assistant(welcomeMessage);
            this.scrollToBottom();
        }
    }

    async handleSubmit(e) {
        e.preventDefault();

        const query = queryInput?.value.trim();
        if (!query) return;

        // Get attached file if any
        const attachedFile = window.fileAttachmentManager?.getAttachedFile();

        // Clear input and file
        queryInput.value = '';
        if (window.fileAttachmentManager) {
            window.fileAttachmentManager.removeFile();
        }

        // Add user message
        this.addMessage('user', query);

        // Add to chat history
        if (window.chatHistoryManager) {
            window.chatHistoryManager.addMessageToHistory('user', query);
        }

        // Show loading
        this.addMessage('loading');

        try {
            const response = await this.sendQuery(query, attachedFile);
            this.removeLoadingMessage();
            this.addMessage('assistant', response);

            // Add to chat history
            if (window.chatHistoryManager) {
                window.chatHistoryManager.addMessageToHistory('assistant', response);
            }
        } catch (error) {
            this.removeLoadingMessage();
            this.addMessage('error', error.message);
        }
    }

    addMessage(type, content = null, saveToHistory = true) {
        if (!chatMessages) return;

        let messageHtml = '';

        switch (type) {
            case 'user':
                messageHtml = this.templates.user(content);
                if (saveToHistory) {
                    this.messages.push({ type, content, timestamp: Date.now() });
                }
                break;
            case 'assistant':
                messageHtml = this.templates.assistant(content);
                if (saveToHistory) {
                    this.messages.push({ type, content, timestamp: Date.now() });
                }
                break;
            case 'loading':
                messageHtml = this.templates.loading();
                break;
            case 'error':
                messageHtml = this.templates.error(content);
                break;
        }

        chatMessages.insertAdjacentHTML('beforeend', messageHtml);
        this.scrollToBottom();
    }

    regenerateLastResponse() {
        // Find the last user message and resend it
        const lastUserMessage = this.messages.slice().reverse().find(msg => msg.type === 'user');
        if (lastUserMessage) {
            // Remove the last assistant message
            const lastAssistantMsg = chatMessages.querySelector('.message.assistant-message:last-of-type');
            if (lastAssistantMsg) {
                lastAssistantMsg.remove();
            }

            // Resend the query
            this.addMessage('loading');
            this.sendQuery(lastUserMessage.content).then(response => {
                this.removeLoadingMessage();
                this.addMessage('assistant', response);
            }).catch(error => {
                this.removeLoadingMessage();
                this.addMessage('error', error.message);
            });
        }
    }

    removeLoadingMessage() {
        const loadingMessage = chatMessages?.querySelector('.loading-message');
        const typingIndicator = chatMessages?.querySelector('.typing-indicator');
        if (loadingMessage) {
            loadingMessage.remove();
        }
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    async sendQuery(query, attachedFile = null) {
        console.log('🚀 sendQuery called with:', query);
        console.log('📎 File attached:', !!attachedFile);
        
        try {
            if (attachedFile) {
                // Use form data for file uploads
                const formData = new FormData();
                formData.append('query', query);
                formData.append('file', attachedFile);
                
                const response = await fetch('/query', {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const result = await response.json();
                console.log('✅ Query successful with file:', result);
                return result;
            } else {
                // Use JSON for text-only queries
                const response = await fetch('/query-json', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ query: query })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const result = await response.json();
                console.log('✅ Query successful:', result);
                return result;
            }
        } catch (error) {
            console.error('❌ Query failed:', error);

            // Return error response for real system
            return {
                answer: "I apologize, but I'm experiencing technical difficulties right now. Please check your internet connection and try again. If the problem persists, ensure the server is running and API keys are properly configured.",
                sources: [],
                confidence: 0.0,
                processing_time: 0.0,
                question_type: "error"
            };
        }
    }

    formatEducationalContent(content) {
        if (!content) return '';

        // Apply educational formatting rules
        let formatted = content
            // Bold headings for main concepts
            .replace(/^(.*?):$/gm, '<h3>$1</h3>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            // Italics for key terms
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            // Underline for important points
            .replace(/__(.*?)__/g, '<u>$1</u>')
            // Code/formulas
            .replace(/`(.*?)`/g, '<code>$1</code>')
            // Blockquotes for textbook excerpts
            .replace(/^> (.*$)/gm, '<blockquote>$1</blockquote>')
            // Numbered lists
            .replace(/^\d+\.\s+(.*$)/gm, '<li>$1</li>')
            // Bullet points
            .replace(/^[•·]\s+(.*$)/gm, '<li>$1</li>')
            // Line breaks
            .replace(/\n/g, '<br>');

        // Wrap consecutive list items
        formatted = formatted.replace(/(<li>.*?<\/li>)(\s*<li>.*?<\/li>)+/g, (match) => {
            if (match.includes('1.') || /^\d+\./.test(match)) {
                return `<ol>${match}</ol>`;
            } else {
                return `<ul>${match}</ul>`;
            }
        });

        return formatted;
    }

    createEnhancedSourcesSection(sources) {
        if (!sources || sources.length === 0) return '';

        return `
            <div class="source-citation">
                <strong>📚 Sources</strong>
                <div class="source-list">
                    ${sources.map(source => `
                        <div class="source-item" data-subject="${source.subject || 'General'}" onclick="this.classList.toggle('expanded')">
                            <div class="source-info">
                                <div class="source-title">${this.escapeHtml(source.source)}</div>
                                <div class="source-meta">Page ${source.page || 'N/A'} • ${source.subject || 'General'}</div>
                            </div>
                            <div class="source-relevance">
                                <div class="relevance-bar">
                                    <div class="relevance-fill" style="width: ${(source.score * 100).toFixed(0)}%"></div>
                                </div>
                                <span>${(source.score * 100).toFixed(1)}%</span>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    createQuickActions(response) {
        const actions = [
            { text: 'Explain Further', icon: 'fas fa-plus-circle' },
            { text: 'Show Examples', icon: 'fas fa-lightbulb' },
            { text: 'Create Quiz', icon: 'fas fa-question-circle' }
        ];

        const relatedTopics = this.generateRelatedTopics(response);

        return `
            <div class="quick-actions" style="margin-top: 16px; display: flex; gap: 8px; flex-wrap: wrap;">
                ${actions.map(action => `
                    <button class="quick-action-btn" data-action="${action.text.toLowerCase().replace(' ', '-')}"
                            style="padding: 6px 12px; border: 1px solid #e2e8f0; background: white; border-radius: 16px;
                                   font-size: 0.8rem; color: #64748b; cursor: pointer; transition: all 0.2s ease;
                                   display: flex; align-items: center; gap: 6px;">
                        <i class="${action.icon}"></i>
                        ${action.text}
                    </button>
                `).join('')}
            </div>
            ${relatedTopics}
        `;
    }

    generateRelatedTopics(response) {
        // Extract subject from sources or classify content
        const subject = response.sources?.[0]?.subject || this.classifySubject(response.answer);
        const topics = this.getRelatedTopicsBySubject(subject);

        if (topics.length === 0) return '';

        return `
            <div class="related-topics">
                <h4>Related Topics</h4>
                <div class="topic-tags">
                    ${topics.map(topic => `
                        <span class="topic-tag" onclick="document.getElementById('query-input').value='Tell me about ${topic}'; document.getElementById('query-input').focus();">
                            ${topic}
                        </span>
                    `).join('')}
                </div>
            </div>
        `;
    }

    classifySubject(content) {
        const keywords = {
            'History': ['revolution', 'war', 'empire', 'colonial', 'independence', 'ancient', 'medieval'],
            'Geography': ['climate', 'mountain', 'river', 'population', 'agriculture', 'natural', 'physical'],
            'Economics': ['economy', 'trade', 'development', 'poverty', 'globalization', 'market', 'resources'],
            'Political Science': ['democracy', 'government', 'constitution', 'rights', 'election', 'political', 'citizen']
        };

        const contentLower = content.toLowerCase();
        let maxMatches = 0;
        let detectedSubject = 'General';

        for (const [subject, words] of Object.entries(keywords)) {
            const matches = words.filter(word => contentLower.includes(word)).length;
            if (matches > maxMatches) {
                maxMatches = matches;
                detectedSubject = subject;
            }
        }

        return detectedSubject;
    }

    getRelatedTopicsBySubject(subject) {
        const topicMap = {
            'History': ['French Revolution', 'Industrial Revolution', 'World Wars', 'Independence Movement', 'Ancient Civilizations'],
            'Geography': ['Climate Change', 'Natural Resources', 'Population Distribution', 'Physical Features', 'Agriculture'],
            'Economics': ['Globalization', 'Poverty', 'Development', 'Trade', 'Economic Systems'],
            'Political Science': ['Democracy', 'Constitution', 'Rights and Duties', 'Elections', 'Government Structure'],
            'General': ['Study Tips', 'Exam Preparation', 'Key Concepts', 'Important Topics']
        };

        return (topicMap[subject] || topicMap['General']).slice(0, 4);
    }

    showDemo() {
        alert('Demo feature coming soon! Click "Get Started" to try the AI assistant.');
    }

    scrollToBottom() {
        if (chatMessages) {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
    }

    getTimestamp() {
        return new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    escapeHtml(unsafe) {
        if (!unsafe) return '';
        return unsafe
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }
}

// CTA Buttons Controller
class CTAController {
    constructor() {
        this.init();
    }

    init() {
        this.setupCTAButtons();
    }

    setupCTAButtons() {
        // Book Counselling
        const bookingBtn = document.querySelector('.cta-btn:first-child');
        if (bookingBtn) {
            bookingBtn.addEventListener('click', () => {
                this.handleBooking();
            });
        }

        // Download App
        const downloadBtn = document.querySelector('.cta-btn:nth-child(2)');
        if (downloadBtn) {
            downloadBtn.addEventListener('click', () => {
                this.handleDownload();
            });
        }

        // WhatsApp
        const whatsappBtn = document.querySelector('.cta-btn:last-child');
        if (whatsappBtn) {
            whatsappBtn.addEventListener('click', () => {
                this.handleWhatsApp();
            });
        }
    }

    handleBooking() {
        window.open('https://calendly.com/your-booking-link', '_blank');
    }

    handleDownload() {
        window.open('https://apps.apple.com/your-app-link', '_blank');
    }

    handleWhatsApp() {
        const message = encodeURIComponent('Hi! I\'m interested in learning more about the Educational RAG AI App.');
        window.open(`https://wa.me/1234567890?text=${message}`, '_blank');
    }
}

// Accessibility Controller
class AccessibilityController {
    constructor() {
        this.init();
    }

    init() {
        this.setupKeyboardNavigation();
        this.setupAriaLabels();
        this.setupFocusManagement();
    }

    setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            // Escape key closes chat
            if (e.key === 'Escape' && chatInterface && !chatInterface.classList.contains('hidden')) {
                chatController.closeChat();
            }
            
            // Enter key on buttons
            if (e.key === 'Enter' && e.target.classList.contains('btn-primary')) {
                e.target.click();
            }
        });
    }

    setupAriaLabels() {
        // Add ARIA labels for better screen reader support
        const buttons = document.querySelectorAll('button');
        buttons.forEach(btn => {
            if (!btn.getAttribute('aria-label')) {
                btn.setAttribute('aria-label', btn.textContent.trim());
            }
        });
        
        // Chat interface
        if (chatInterface) {
            chatInterface.setAttribute('role', 'dialog');
            chatInterface.setAttribute('aria-modal', 'true');
            chatInterface.setAttribute('aria-labelledby', 'chat-title');
        }
    }

    setupFocusManagement() {
        const focusableElements = 'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])';
        
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab' && chatInterface && !chatInterface.classList.contains('hidden')) {
                const focusable = chatInterface.querySelectorAll(focusableElements);
                const firstFocusable = focusable[0];
                const lastFocusable = focusable[focusable.length - 1];
                
                if (e.shiftKey) {
                    if (document.activeElement === firstFocusable) {
                        lastFocusable.focus();
                        e.preventDefault();
                    }
                } else {
                    if (document.activeElement === lastFocusable) {
                        firstFocusable.focus();
                        e.preventDefault();
                    }
                }
            }
        });
    }
}

// Initialize DOM Elements
function initializeDOMElements() {
    // Landing Page Elements
    getStartedBtn = document.getElementById('get-started-btn');
    startChatBtn = document.getElementById('start-chat-btn');
    floatingChatBtn = document.getElementById('floating-chat-btn');
    watchDemoBtn = document.getElementById('watch-demo-btn');
    chatInterface = document.getElementById('chat-interface');
    closeChatBtn = document.getElementById('close-chat');

    // Modern Chat Interface Elements
    chatSidebar = document.getElementById('chat-sidebar');
    toggleSidebarBtn = document.getElementById('toggle-sidebar-btn');
    newChatBtn = document.getElementById('new-chat-btn');
    chatSearchInput = document.getElementById('chat-search-input');
    chatHistoryList = document.getElementById('chat-history-list');
    clearHistoryBtn = document.getElementById('clear-history-btn');

    // Chat Header Elements
    themeToggleBtn = document.getElementById('theme-toggle-btn');
    minimizeChatBtn = document.getElementById('minimize-chat-btn');
    maximizeChatBtn = document.getElementById('maximize-chat-btn');

    // Chat Messages Elements
    chatMessages = document.getElementById('chat-messages');
    queryForm = document.getElementById('query-form');
    queryInput = document.getElementById('query-input');

    // Enhanced Input Elements
    attachBtn = document.getElementById('attach-btn');
    fileInput = document.getElementById('file-input');
    filePreview = document.getElementById('file-preview');
    voiceBtn = document.getElementById('voice-btn');
    voiceRecording = document.getElementById('voice-recording');
    emojiBtn = document.getElementById('emoji-btn');
    emojiPicker = document.getElementById('emoji-picker');
    emojiGrid = document.getElementById('emoji-grid');

    console.log('🔍 DOM Elements initialized:', {
        getStartedBtn: !!getStartedBtn,
        startChatBtn: !!startChatBtn,
        floatingChatBtn: !!floatingChatBtn,
        chatInterface: !!chatInterface
    });
}

// Initialize
async function updateStatus() {
    const health = await checkHealth();
    
    statusIndicators.innerHTML = Object.entries(health.components)
        .map(([name, status]) => createStatusIndicator(name, status))
        .join('');
}

// Add CSS animations via JavaScript for dynamic effects
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .loaded .fade-in {
        animation: fadeInUp 0.6s ease forwards;
    }
`;
document.head.appendChild(style);

// Global variables
let animationController;
let chatController;
let ctaController;
let accessibilityController;
let themeManager;
let chatHistoryManager;
let fileAttachmentManager;
let voiceRecordingManager;
let emojiPickerManager;
let sidebarManager;

// Initialize all controllers when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 DOM Content Loaded - Initializing Modern Chat App...');

    // CRITICAL: Initialize DOM elements FIRST before any controllers
    initializeDOMElements();

    // Immediate verification that DOM elements are found
    console.log('🔍 DOM Element Verification:');
    console.log('- Get Started Button:', !!getStartedBtn, getStartedBtn);
    console.log('- Start Chat Button:', !!startChatBtn, startChatBtn);
    console.log('- Floating Chat Button:', !!floatingChatBtn, floatingChatBtn);
    console.log('- Chat Interface:', !!chatInterface, chatInterface);

    if (!getStartedBtn) console.error('❌ CRITICAL: Get Started button NOT found!');
    if (!startChatBtn) console.error('❌ CRITICAL: Start Chat button NOT found!');
    if (!floatingChatBtn) console.error('❌ CRITICAL: Floating Chat button NOT found!');
    if (!chatInterface) console.error('❌ CRITICAL: Chat Interface NOT found!');

    // Initialize all controllers AFTER DOM elements are ready
    animationController = new AnimationController();
    themeManager = new ThemeManager();
    chatHistoryManager = new ChatHistoryManager();
    fileAttachmentManager = new FileAttachmentManager();
    voiceRecordingManager = new VoiceRecordingManager();
    emojiPickerManager = new EmojiPickerManager();
    sidebarManager = new SidebarManager();
    chatController = new ChatController();
    ctaController = new CTAController();
    accessibilityController = new AccessibilityController();

    // Make controllers globally available
    window.chatController = chatController;
    window.themeManager = themeManager;
    window.chatHistoryManager = chatHistoryManager;
    window.fileAttachmentManager = fileAttachmentManager;
    window.voiceRecordingManager = voiceRecordingManager;
    window.emojiPickerManager = emojiPickerManager;
    window.sidebarManager = sidebarManager;

    // Add loading complete class to body
    setTimeout(() => {
        document.body.classList.add('loaded');
    }, 100);

    console.log('✅ Modern Educational RAG App with Advanced Features initialized successfully!');

    // Update status every 30 seconds (if status elements exist)
    if (typeof updateStatus === 'function') {
        updateStatus();
        setInterval(updateStatus, 30000);
    }
});