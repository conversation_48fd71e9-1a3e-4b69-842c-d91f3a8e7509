#!/usr/bin/env python3
"""
Setup script to connect to Pinecone and initialize the vector database
with CBSE textbook data for the agentic RAG system.
"""

import os
import json
import time
import logging
from typing import List, Dict, Any
from dotenv import load_dotenv
from sentence_transformers import SentenceTransformer
import pinecone
from tqdm import tqdm

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PineconeSetup:
    """Setup and initialize Pinecone for the agentic RAG system."""
    
    def __init__(self):
        self.api_key = os.getenv('PINECONE_API_KEY')
        self.index_name = os.getenv('PINECONE_INDEX_NAME', 'virat-gyankosh-cbse')
        self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
        self.dimension = 384  # Dimension for all-MiniLM-L6-v2
        
    def check_api_key(self):
        """Check if Pinecone API key is valid."""
        if not self.api_key or self.api_key.startswith('your-'):
            raise ValueError("Please set a valid PINECONE_API_KEY in your .env file")
        logger.info(f"✅ Pinecone API key found: {'*' * 10}...{self.api_key[-4:]}")
        
    def initialize_pinecone(self):
        """Initialize Pinecone with the correct configuration."""
        try:
            from pinecone import Pinecone
            # Initialize Pinecone (new API for v2.2.4+)
            self.pc = Pinecone(api_key=self.api_key)
            logger.info("✅ Pinecone initialized successfully")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to initialize Pinecone: {e}")
            return False
    
    def create_index(self):
        """Create Pinecone index if it doesn't exist."""
        try:
            # List existing indexes
            existing_indexes = [idx.name for idx in self.pc.list_indexes()]
            logger.info(f"📊 Existing indexes: {existing_indexes}")
            
            if self.index_name not in existing_indexes:
                logger.info(f"🔨 Creating index: {self.index_name}")
                from pinecone import ServerlessSpec
                self.pc.create_index(
                    name=self.index_name,
                    dimension=self.dimension,
                    metric='cosine',
                    spec=ServerlessSpec(
                        cloud='aws',
                        region='us-east-1'
                    )
                )
                
                # Wait for index to be ready
                logger.info("⏳ Waiting for index to be ready...")
                time.sleep(60)  # Wait for index creation
                
            # Connect to index
            self.index = self.pc.Index(self.index_name)
            logger.info(f"✅ Connected to index: {self.index_name}")
            
            # Get index stats
            stats = self.index.describe_index_stats()
            logger.info(f"📈 Index stats: {stats}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to create/connect to index: {e}")
            return False
    
    def load_textbook_data(self):
        """Load CBSE textbook data from processed chunks."""
        chunks_dir = "processed_chunks"
        all_chunks = []
        
        if not os.path.exists(chunks_dir):
            logger.error(f"❌ Chunks directory not found: {chunks_dir}")
            return []
        
        # Load all chunk files
        for filename in os.listdir(chunks_dir):
            if filename.endswith('_chunks.json'):
                filepath = os.path.join(chunks_dir, filename)
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # Extract source name and subject
                    source_name = filename.replace('_chunks.json', '')
                    subject = self.get_subject_from_filename(source_name)
                    
                    # Process chunks
                    for i, chunk in enumerate(data):
                        chunk_data = {
                            'id': f"{source_name}_{i}",
                            'text': chunk.get('text', ''),
                            'source': source_name,
                            'subject': subject,
                            'page': chunk.get('page', 0),
                            'chunk_id': i
                        }
                        all_chunks.append(chunk_data)
                    
                    logger.info(f"📚 Loaded {len(data)} chunks from {filename}")
                    
                except Exception as e:
                    logger.error(f"❌ Error loading {filepath}: {e}")
        
        logger.info(f"📊 Total chunks loaded: {len(all_chunks)}")
        return all_chunks
    
    def get_subject_from_filename(self, filename):
        """Extract subject from filename."""
        filename_lower = filename.lower()
        if 'geography' in filename_lower or 'contemporary india' in filename_lower:
            return 'Geography'
        elif 'history' in filename_lower or 'contemporary world' in filename_lower:
            return 'History'
        elif 'economics' in filename_lower:
            return 'Economics'
        elif 'political' in filename_lower or 'democratic politics' in filename_lower:
            return 'Political Science'
        else:
            return 'General'
    
    def upload_to_pinecone(self, chunks):
        """Upload chunks to Pinecone with embeddings."""
        if not chunks:
            logger.error("❌ No chunks to upload")
            return False
        
        logger.info(f"🚀 Starting upload of {len(chunks)} chunks to Pinecone...")
        
        # Process in batches
        batch_size = 100
        total_batches = (len(chunks) + batch_size - 1) // batch_size
        
        for batch_idx in tqdm(range(total_batches), desc="Uploading batches"):
            start_idx = batch_idx * batch_size
            end_idx = min(start_idx + batch_size, len(chunks))
            batch_chunks = chunks[start_idx:end_idx]
            
            # Prepare vectors for this batch
            vectors = []
            texts = [chunk['text'] for chunk in batch_chunks]
            
            # Generate embeddings
            embeddings = self.embedding_model.encode(texts, normalize_embeddings=True)
            
            # Prepare vectors for Pinecone
            for i, chunk in enumerate(batch_chunks):
                vector = {
                    'id': chunk['id'],
                    'values': embeddings[i].tolist(),
                    'metadata': {
                        'text': chunk['text'][:1000],  # Limit text size
                        'source': chunk['source'],
                        'subject': chunk['subject'],
                        'page': chunk['page'],
                        'chunk_id': chunk['chunk_id']
                    }
                }
                vectors.append(vector)
            
            # Upload batch to Pinecone
            try:
                self.index.upsert(vectors=vectors)
                logger.info(f"✅ Uploaded batch {batch_idx + 1}/{total_batches}")
            except Exception as e:
                logger.error(f"❌ Failed to upload batch {batch_idx + 1}: {e}")
                return False
            
            # Small delay to avoid rate limits
            time.sleep(0.1)
        
        logger.info("🎉 Successfully uploaded all chunks to Pinecone!")
        return True
    
    def verify_upload(self):
        """Verify that data was uploaded correctly."""
        try:
            stats = self.index.describe_index_stats()
            logger.info(f"📊 Final index stats: {stats}")
            
            # Test query
            test_embedding = self.embedding_model.encode(["What is democracy?"]).tolist()
            result = self.index.query(
                vector=test_embedding[0],
                top_k=3,
                include_metadata=True
            )
            
            if result.matches:
                logger.info("✅ Test query successful!")
                for match in result.matches:
                    logger.info(f"   📝 Score: {match.score:.3f}, Source: {match.metadata.get('source', 'Unknown')}")
                return True
            else:
                logger.warning("⚠️ Test query returned no results")
                return False
                
        except Exception as e:
            logger.error(f"❌ Verification failed: {e}")
            return False

def main():
    """Main setup function."""
    print("🚀 Pinecone Setup for Virat Gyankosh Agentic RAG")
    print("=" * 60)
    
    setup = PineconeSetup()
    
    try:
        # Step 1: Check API key
        setup.check_api_key()
        
        # Step 2: Initialize Pinecone
        if not setup.initialize_pinecone():
            logger.error("❌ Failed to initialize Pinecone")
            return False
        
        # Step 3: Create/connect to index
        if not setup.create_index():
            logger.error("❌ Failed to create/connect to index")
            return False
        
        # Step 4: Load textbook data
        chunks = setup.load_textbook_data()
        if not chunks:
            logger.error("❌ No textbook data found")
            return False
        
        # Step 5: Upload to Pinecone
        if not setup.upload_to_pinecone(chunks):
            logger.error("❌ Failed to upload data")
            return False
        
        # Step 6: Verify upload
        if not setup.verify_upload():
            logger.error("❌ Upload verification failed")
            return False
        
        print("\n🎉 PINECONE SETUP COMPLETE!")
        print("=" * 60)
        print("✅ Pinecone is now connected and ready")
        print("✅ CBSE textbook data uploaded successfully")
        print("✅ Agentic RAG system is fully functional")
        print(f"✅ Index name: {setup.index_name}")
        print(f"✅ Total vectors: {len(chunks)}")
        print("\n🚀 You can now run the enhanced RAG server!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Setup failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Setup failed. Please check the logs and try again.")
        exit(1)
