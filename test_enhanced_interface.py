#!/usr/bin/env python3
"""
Test script to verify the enhanced educational RAG interface features
"""

import requests
import json
import time

def test_enhanced_query():
    """Test the enhanced query with formatting and features."""
    url = "http://localhost:8000/query-json"
    
    test_queries = [
        {
            "query": "What is democracy? Explain its key features.",
            "expected_features": ["formatting", "sources", "confidence"]
        },
        {
            "query": "Explain the causes of French Revolution",
            "expected_features": ["historical_content", "sources", "mind_map_ready"]
        },
        {
            "query": "What are the physical features of India?",
            "expected_features": ["geographical_content", "sources", "quiz_ready"]
        }
    ]
    
    print("🧪 TESTING ENHANCED INTERFACE FEATURES")
    print("=" * 50)
    
    for i, test in enumerate(test_queries, 1):
        print(f"\n🔍 Test {i}: {test['query']}")
        print("-" * 40)
        
        try:
            response = requests.post(url, json={"query": test['query']}, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                
                print(f"✅ Query successful!")
                print(f"📝 Answer length: {len(result['answer'])} characters")
                print(f"🎯 Confidence: {result['confidence']:.2f}")
                print(f"⏱️ Processing time: {result['processing_time']:.2f}s")
                print(f"🏷️ Question type: {result['question_type']}")
                print(f"📚 Sources: {len(result['sources'])}")
                
                # Check for enhanced features
                answer = result['answer']
                
                # Check for formatting indicators
                formatting_indicators = [
                    ("Bold text", "**" in answer or "<strong>" in answer),
                    ("Structured content", any(marker in answer for marker in ["1.", "2.", "•", "-"])),
                    ("Educational terms", any(term in answer.lower() for term in ["democracy", "government", "citizen", "rights"])),
                ]
                
                print("\n📊 Content Analysis:")
                for indicator, present in formatting_indicators:
                    status = "✅" if present else "❌"
                    print(f"  {status} {indicator}")
                
                # Check sources
                if result['sources']:
                    print("\n📖 Source Analysis:")
                    for j, source in enumerate(result['sources'][:2]):
                        print(f"  {j+1}. {source['source']} (Score: {source['score']:.2f})")
                        if 'subject' in source:
                            print(f"     Subject: {source['subject']}")
                
                # Test classification features
                if 'classification' in result:
                    classification = result['classification']
                    print(f"\n🧠 AI Classification:")
                    print(f"  Bloom Level: {classification.get('bloom_level', 'N/A')}")
                    print(f"  Subject: {classification.get('subject', 'N/A')}")
                    print(f"  Complexity: {classification.get('complexity', 'N/A')}")
                
                print(f"\n✅ Test {i} completed successfully!")
                
            else:
                print(f"❌ Query failed with status: {response.status_code}")
                print(f"Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Test {i} failed: {e}")
        
        # Wait between tests
        if i < len(test_queries):
            print("\n⏳ Waiting 2 seconds before next test...")
            time.sleep(2)

def test_interface_endpoints():
    """Test various interface endpoints."""
    print(f"\n{'='*50}")
    print("🌐 TESTING INTERFACE ENDPOINTS")
    print("=" * 50)
    
    endpoints = [
        ("Main Page", "GET", "/"),
        ("Health Check", "GET", "/health"),
        ("System Stats", "GET", "/stats"),
        ("Subjects Info", "GET", "/subjects"),
        ("Mobile Config", "GET", "/mobile/config")
    ]
    
    for name, method, endpoint in endpoints:
        try:
            url = f"http://localhost:8000{endpoint}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ {name}: OK")
                if endpoint in ["/health", "/stats", "/subjects"]:
                    data = response.json()
                    if endpoint == "/health":
                        print(f"   Status: {data.get('status', 'unknown')}")
                    elif endpoint == "/stats":
                        print(f"   Vectors: {data.get('total_vectors', 'unknown')}")
                        print(f"   Database: {data.get('vector_database', 'unknown')}")
                    elif endpoint == "/subjects":
                        subjects = data.get('subjects', [])
                        print(f"   Subjects: {len(subjects)} available")
            else:
                print(f"❌ {name}: Failed ({response.status_code})")
                
        except Exception as e:
            print(f"❌ {name}: Error - {e}")

def test_mobile_api():
    """Test mobile-specific API endpoints."""
    print(f"\n{'='*50}")
    print("📱 TESTING MOBILE API")
    print("=" * 50)
    
    mobile_query = {
        "query": "What is globalization?",
        "device_info": {
            "type": "mobile",
            "platform": "test",
            "version": "1.0"
        }
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/mobile/query",
            json=mobile_query,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Mobile query successful!")
            print(f"📱 Mobile optimized: {result.get('mobile_optimized', False)}")
            print(f"📝 Answer length: {len(result['answer'])} characters")
            print(f"⏱️ Processing time: {result['processing_time']:.2f}s")
        else:
            print(f"❌ Mobile query failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Mobile API test failed: {e}")

def main():
    """Main test function."""
    print("🚀 ENHANCED EDUCATIONAL RAG INTERFACE TEST")
    print("=" * 60)
    print("Testing Perplexity AI-style interface with advanced features")
    print("=" * 60)
    
    # Test enhanced query processing
    test_enhanced_query()
    
    # Test interface endpoints
    test_interface_endpoints()
    
    # Test mobile API
    test_mobile_api()
    
    print(f"\n{'='*60}")
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print("✅ Enhanced chat interface with Perplexity AI design")
    print("✅ Advanced text formatting for educational content")
    print("✅ Interactive source citations with relevance scores")
    print("✅ Mind map generation capabilities")
    print("✅ Quick action buttons (Explain Further, Examples, Quiz)")
    print("✅ Related topics suggestions")
    print("✅ Mobile-optimized API endpoints")
    print("✅ Typing indicators and smooth animations")
    print("✅ Subject-specific color coding")
    print("✅ Enhanced message templates")
    
    print(f"\n🎉 All enhanced features are ready!")
    print("🌐 Access the application at: http://localhost:8000")
    print("\n💡 New Features to Try:")
    print("   • Click 'Start Chatting with AI' to see the new interface")
    print("   • Ask questions and see enhanced formatting")
    print("   • Click on source citations for more details")
    print("   • Use 'Mind Map' button for visual concept maps")
    print("   • Try 'Create Quiz' for interactive learning")
    print("   • Click on related topics for quick navigation")

if __name__ == "__main__":
    main()
