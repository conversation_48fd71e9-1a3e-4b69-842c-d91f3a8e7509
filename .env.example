# Environment Configuration for Virat Gyankosh RAG System
# Copy this file to .env and fill in your API keys

# OpenAI API Key (required for RAG functionality)
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Pinecone API Key (optional - for cloud vector storage)
# Get your API key from: https://www.pinecone.io/
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=your_pinecone_environment_here

# Application Settings
DEBUG=False
LOG_LEVEL=INFO

# Note: The system will work with just the OpenAI API key
# Pinecone is optional and used for enhanced cloud features
