#!/usr/bin/env python3
"""
Test script to check RAG service functionality
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_environment():
    """Test if environment variables are set correctly."""
    print("🔍 Testing Environment Variables...")
    
    openai_key = os.getenv('OPENAI_API_KEY')
    pinecone_key = os.getenv('PINECONE_API_KEY')
    
    if openai_key and openai_key.startswith('sk-'):
        print("✅ OpenAI API key found")
    else:
        print("❌ OpenAI API key missing or invalid")
        return False
    
    if pinecone_key and pinecone_key.startswith('pcsk_'):
        print("✅ Pinecone API key found")
    else:
        print("❌ Pinecone API key missing or invalid")
        return False
    
    return True

def test_imports():
    """Test if all required packages can be imported."""
    print("\n🔍 Testing Package Imports...")
    
    try:
        import fastapi
        print("✅ FastAPI imported")
    except ImportError as e:
        print(f"❌ FastAPI import failed: {e}")
        return False
    
    try:
        import uvicorn
        print("✅ Uvicorn imported")
    except ImportError as e:
        print(f"❌ Uvicorn import failed: {e}")
        return False
    
    try:
        from sentence_transformers import SentenceTransformer
        print("✅ SentenceTransformers imported")
    except ImportError as e:
        print(f"❌ SentenceTransformers import failed: {e}")
        return False
    
    try:
        from pinecone import Pinecone
        print("✅ Pinecone imported")
    except ImportError as e:
        print(f"❌ Pinecone import failed: {e}")
        return False
    
    try:
        from openai import OpenAI
        print("✅ OpenAI imported")
    except ImportError as e:
        print(f"❌ OpenAI import failed: {e}")
        return False
    
    return True

def test_rag_service():
    """Test RAG service initialization."""
    print("\n🔍 Testing RAG Service...")
    
    try:
        from agentic_rag_service import AgenticRAGService
        print("✅ RAG service module imported")
        
        # Try to initialize the service
        print("🚀 Initializing RAG service...")
        rag_service = AgenticRAGService()
        print("✅ RAG service initialized successfully")
        
        # Test health check
        print("🏥 Running health check...")
        health = rag_service.health_check()
        print(f"✅ Health check result: {health['status']}")
        
        return True
        
    except Exception as e:
        print(f"❌ RAG service test failed: {e}")
        return False

def test_simple_query():
    """Test a simple query."""
    print("\n🔍 Testing Simple Query...")
    
    try:
        from agentic_rag_service import AgenticRAGService
        rag_service = AgenticRAGService()
        
        # Test query
        test_query = "What is democracy?"
        print(f"🤔 Testing query: {test_query}")
        
        result = rag_service.process_query(test_query)
        
        if result and result.get('answer'):
            print("✅ Query processed successfully")
            print(f"📝 Answer preview: {result['answer'][:100]}...")
            print(f"🎯 Confidence: {result.get('confidence', 0):.2f}")
            print(f"⏱️ Processing time: {result.get('processing_time', 0):.2f}s")
            return True
        else:
            print("❌ Query failed - no answer received")
            return False
            
    except Exception as e:
        print(f"❌ Query test failed: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 RAG SERVICE DIAGNOSTIC TEST")
    print("=" * 50)
    
    tests = [
        ("Environment Variables", test_environment),
        ("Package Imports", test_imports),
        ("RAG Service", test_rag_service),
        ("Simple Query", test_simple_query)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*50}")
    print("📊 TEST SUMMARY")
    print(f"{'='*50}")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! RAG service is ready.")
        return True
    else:
        print("⚠️ Some tests failed. Check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
